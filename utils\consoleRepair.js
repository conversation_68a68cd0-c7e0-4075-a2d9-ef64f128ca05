/**
 * 控制台修复工具
 * 可以在微信开发者工具的控制台中直接运行
 */

// 将修复工具挂载到全局，方便在控制台调用
const consoleRepair = {
  /**
   * 快速修复数据库 - 在控制台运行: repairDB()
   */
  async repairDB() {
    console.log('🔧 开始数据库修复...');
    
    try {
      // 检查云开发是否可用
      if (!wx.cloud) {
        console.error('❌ 云开发不可用');
        return false;
      }

      const db = wx.cloud.database();
      console.log('✅ 云开发连接成功');

      // 需要创建的集合列表
      const collections = [
        'users',
        'students', 
        'records',
        'comments',
        'behavior_records',
        'user_profiles',
        'user_consent_records',
        'ai_configs',
        'settings'
      ];

      const results = {
        existing: [],
        created: [],
        failed: []
      };

      // 检查并创建集合
      for (const collectionName of collections) {
        try {
          console.log(`🔍 检查集合: ${collectionName}`);
          
          // 尝试访问集合
          await db.collection(collectionName).limit(1).get();
          results.existing.push(collectionName);
          console.log(`✅ ${collectionName}: 已存在`);
          
        } catch (error) {
          if (error.errCode === -502005) {
            // 集合不存在，尝试创建
            console.log(`❌ ${collectionName}: 不存在，正在创建...`);
            
            try {
              // 创建集合（通过添加临时文档）
              const initData = this.getInitData(collectionName);
              const addResult = await db.collection(collectionName).add({
                data: initData
              });
              
              // 立即删除临时文档
              await db.collection(collectionName).doc(addResult._id).remove();
              
              results.created.push(collectionName);
              console.log(`✅ ${collectionName}: 创建成功`);
              
            } catch (createError) {
              console.error(`❌ ${collectionName}: 创建失败 -`, createError);
              results.failed.push(collectionName);
            }
          } else {
            console.error(`⚠️ ${collectionName}: 访问异常 -`, error);
            results.failed.push(collectionName);
          }
        }
      }

      // 输出结果
      console.log('\n📊 修复结果:');
      console.log('='.repeat(50));
      console.log(`✅ 已存在集合 (${results.existing.length}个):`, results.existing);
      console.log(`🆕 新创建集合 (${results.created.length}个):`, results.created);
      console.log(`❌ 创建失败集合 (${results.failed.length}个):`, results.failed);
      console.log('='.repeat(50));

      if (results.failed.length === 0) {
        console.log('🎉 数据库修复完成！所有集合都已就绪。');
        return true;
      } else {
        console.log('⚠️ 部分集合创建失败，请检查权限或网络连接。');
        return false;
      }

    } catch (error) {
      console.error('❌ 数据库修复失败:', error);
      return false;
    }
  },

  /**
   * 获取集合的初始数据
   */
  getInitData(collectionName) {
    const baseData = {
      _temp: true,
      _created_by: 'console_repair',
      _created_at: new Date().toISOString(),
      _purpose: '控制台修复工具创建'
    };

    switch (collectionName) {
      case 'behavior_records':
        return {
          ...baseData,
          teacherId: 'console_repair',
          studentId: 'console_repair',
          studentName: '控制台修复测试',
          behavior: '集合创建',
          score: 0,
          createTime: new Date()
        };
      
      case 'user_profiles':
        return {
          ...baseData,
          openid: 'console_repair',
          userInfo: {
            name: '控制台修复',
            nickName: '修复'
          },
          updateTime: new Date()
        };
      
      default:
        return baseData;
    }
  },

  /**
   * 快速检查数据库状态 - 在控制台运行: checkDB()
   */
  async checkDB() {
    console.log('🔍 开始检查数据库状态...');
    
    try {
      if (!wx.cloud) {
        console.error('❌ 云开发不可用');
        return false;
      }

      const db = wx.cloud.database();
      const collections = ['behavior_records', 'students', 'user_profiles', 'users'];
      
      console.log('\n📋 数据库集合状态:');
      console.log('='.repeat(50));

      let allGood = true;

      for (const collection of collections) {
        try {
          await db.collection(collection).limit(1).get();
          console.log(`✅ ${collection}: 正常`);
        } catch (error) {
          if (error.errCode === -502005) {
            console.log(`❌ ${collection}: 不存在`);
            allGood = false;
          } else {
            console.log(`⚠️ ${collection}: 异常 - ${error.message}`);
            allGood = false;
          }
        }
      }

      console.log('='.repeat(50));
      
      if (allGood) {
        console.log('🎉 所有关键集合都正常！');
      } else {
        console.log('⚠️ 发现问题，建议运行 repairDB() 进行修复');
      }

      return allGood;

    } catch (error) {
      console.error('❌ 检查失败:', error);
      return false;
    }
  },

  /**
   * 测试云函数连接 - 在控制台运行: testCloud()
   */
  async testCloud() {
    console.log('☁️ 开始测试云函数连接...');
    
    try {
      const startTime = Date.now();
      
      const res = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getUserId',
          timeout: 10000,
          success: resolve,
          fail: reject
        });
      });

      const responseTime = Date.now() - startTime;
      
      console.log(`✅ 云函数调用成功 (${responseTime}ms)`);
      console.log('📄 返回结果:', res.result);
      
      return true;

    } catch (error) {
      console.error('❌ 云函数调用失败:', error);
      console.log('💡 可能的原因:');
      console.log('   1. 云函数未部署');
      console.log('   2. 网络连接问题');
      console.log('   3. 云开发环境配置错误');
      
      return false;
    }
  },

  /**
   * 显示帮助信息 - 在控制台运行: help()
   */
  help() {
    console.log('\n🔧 数据库修复工具 - 控制台版');
    console.log('='.repeat(50));
    console.log('可用命令:');
    console.log('  checkDB()   - 检查数据库状态');
    console.log('  repairDB()  - 修复数据库（创建缺失集合）');
    console.log('  testCloud() - 测试云函数连接');
    console.log('  help()      - 显示此帮助信息');
    console.log('='.repeat(50));
    console.log('使用方法:');
    console.log('1. 在控制台输入命令，如: checkDB()');
    console.log('2. 按回车键执行');
    console.log('3. 查看输出结果');
    console.log('\n💡 建议先运行 checkDB() 检查状态');
  }
};

// 挂载到全局
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.checkDB = consoleRepair.checkDB.bind(consoleRepair);
  window.repairDB = consoleRepair.repairDB.bind(consoleRepair);
  window.testCloud = consoleRepair.testCloud.bind(consoleRepair);
  window.help = consoleRepair.help.bind(consoleRepair);
} else if (typeof global !== 'undefined') {
  // Node.js环境
  global.checkDB = consoleRepair.checkDB.bind(consoleRepair);
  global.repairDB = consoleRepair.repairDB.bind(consoleRepair);
  global.testCloud = consoleRepair.testCloud.bind(consoleRepair);
  global.help = consoleRepair.help.bind(consoleRepair);
}

// 自动显示帮助信息
setTimeout(() => {
  console.log('\n🎯 数据库修复工具已加载！');
  console.log('💡 在控制台输入 help() 查看可用命令');
}, 1000);

module.exports = consoleRepair;

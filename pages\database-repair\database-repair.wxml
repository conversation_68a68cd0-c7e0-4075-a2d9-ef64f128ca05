<!--数据库修复页面-->
<view class="repair-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">←</text>
    </view>
    <text class="page-title">数据库修复工具</text>
  </view>

  <!-- 网络状态 -->
  <view class="network-status">
    <text class="status-icon">{{networkStatus.isOnline ? '🟢' : '🔴'}}</text>
    <text class="status-text">{{networkStatus.description}}</text>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-section">
    <view class="action-card" bindtap="quickCheck">
      <view class="card-icon">🔍</view>
      <view class="card-content">
        <text class="card-title">快速检查</text>
        <text class="card-desc">检查关键数据库集合状态</text>
      </view>
    </view>

    <view class="action-card" bindtap="repairDatabase">
      <view class="card-icon">🔧</view>
      <view class="card-content">
        <text class="card-title">修复数据库</text>
        <text class="card-desc">创建缺失的数据库集合</text>
      </view>
    </view>

    <view class="action-card" bindtap="testConnection">
      <view class="card-icon">🔗</view>
      <view class="card-content">
        <text class="card-title">测试连接</text>
        <text class="card-desc">测试数据库连接状态</text>
      </view>
    </view>

    <view class="action-card" bindtap="runFullTest">
      <view class="card-icon">☁️</view>
      <view class="card-content">
        <text class="card-title">完整测试</text>
        <text class="card-desc">全面测试云服务功能</text>
      </view>
    </view>
  </view>

  <!-- 状态显示 -->
  <view class="status-section" wx:if="{{repairStatus !== 'ready'}}">
    <view class="status-header">
      <text class="status-title">
        {{repairStatus === 'checking' ? '正在检查...' : 
          repairStatus === 'repairing' ? '正在修复...' : 
          repairStatus === 'completed' ? '操作完成' : '操作失败'}}
      </text>
    </view>

    <!-- 检查结果 -->
    <view class="results-list" wx:if="{{checkResults.length > 0}}">
      <text class="results-title">检查结果:</text>
      <view class="result-item" wx:for="{{checkResults}}" wx:key="name">
        <text class="result-icon">
          {{item.status === 'success' ? '✅' : 
            item.status === 'missing' ? '❌' : '⚠️'}}
        </text>
        <view class="result-content">
          <text class="result-name">{{item.name}}</text>
          <text class="result-message">{{item.message}}</text>
        </view>
      </view>
    </view>

    <!-- 修复结果 -->
    <view class="results-list" wx:if="{{repairResults.length > 0}}">
      <text class="results-title">修复结果:</text>
      <view class="result-item" wx:for="{{repairResults}}" wx:key="*this">
        <text class="result-icon">🔧</text>
        <text class="result-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 重置按钮 -->
  <view class="reset-section" wx:if="{{repairStatus === 'completed' || repairStatus === 'failed'}}">
    <view class="reset-btn" bindtap="resetStatus">
      <text class="reset-text">重新开始</text>
    </view>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <text class="help-title">使用说明:</text>
    <text class="help-text">1. 点击"快速检查"检查数据库状态</text>
    <text class="help-text">2. 如果发现问题，点击"修复数据库"</text>
    <text class="help-text">3. 使用"测试连接"验证修复效果</text>
    <text class="help-text">4. "完整测试"可全面检查云服务</text>
  </view>
</view>

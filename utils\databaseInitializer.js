/**
 * 数据库初始化工具
 * 用于在客户端检测并创建缺失的数据库集合
 */

class DatabaseInitializer {
  constructor() {
    this.requiredCollections = [
      'users',
      'admins', 
      'students',
      'classes',
      'records',
      'comments',
      'behavior_records',
      'user_profiles',
      'user_consent_records',
      'ai_configs',
      'ai_usage',
      'ai_generation_logs',
      'ai_error_logs',
      'settings',
      'system_config',
      'logs',
      'files',
      'notifications'
    ];
    
    this.db = null;
    this.initPromise = null;
  }

  /**
   * 初始化数据库连接
   */
  async init() {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._doInit();
    return this.initPromise;
  }

  async _doInit() {
    try {
      if (!wx.cloud) {
        throw new Error('云开发环境不可用');
      }

      this.db = wx.cloud.database();
      console.log('[数据库初始化器] 初始化完成');
      return true;
    } catch (error) {
      console.error('[数据库初始化器] 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查所有必需的集合是否存在
   */
  async checkAllCollections() {
    await this.init();
    
    const results = {
      existing: [],
      missing: [],
      errors: []
    };

    for (const collectionName of this.requiredCollections) {
      try {
        const exists = await this.checkCollectionExists(collectionName);
        if (exists) {
          results.existing.push(collectionName);
        } else {
          results.missing.push(collectionName);
        }
      } catch (error) {
        console.error(`[数据库初始化器] 检查集合 ${collectionName} 失败:`, error);
        results.errors.push({
          collection: collectionName,
          error: error.message
        });
      }
    }

    console.log('[数据库初始化器] 集合检查结果:', results);
    return results;
  }

  /**
   * 检查单个集合是否存在
   */
  async checkCollectionExists(collectionName) {
    try {
      await this.db.collection(collectionName).limit(1).get();
      return true;
    } catch (error) {
      if (error.errCode === -502005) {
        // 集合不存在
        return false;
      }
      // 其他错误，重新抛出
      throw error;
    }
  }

  /**
   * 创建缺失的集合
   */
  async createMissingCollections(missingCollections) {
    const results = {
      created: [],
      failed: []
    };

    for (const collectionName of missingCollections) {
      try {
        console.log(`[数据库初始化器] 开始创建集合: ${collectionName}`);
        
        const success = await this.createCollection(collectionName);
        if (success) {
          results.created.push(collectionName);
          console.log(`[数据库初始化器] ✅ 集合 ${collectionName} 创建成功`);
        } else {
          results.failed.push(collectionName);
          console.log(`[数据库初始化器] ❌ 集合 ${collectionName} 创建失败`);
        }
      } catch (error) {
        console.error(`[数据库初始化器] 创建集合 ${collectionName} 异常:`, error);
        results.failed.push(collectionName);
      }
    }

    return results;
  }

  /**
   * 创建单个集合
   */
  async createCollection(collectionName) {
    try {
      // 方法1：通过云函数创建
      try {
        const result = await wx.cloud.callFunction({
          name: 'initDatabase',
          data: { 
            action: 'createCollection',
            collectionName: collectionName
          },
          timeout: 30000
        });

        if (result.result && result.result.code === 200) {
          return true;
        }
      } catch (cloudFunctionError) {
        console.warn(`[数据库初始化器] 云函数创建失败，尝试直接创建:`, cloudFunctionError);
      }

      // 方法2：直接创建（添加临时文档）
      const initData = this.getInitialData(collectionName);
      
      const addResult = await this.db.collection(collectionName).add({
        data: initData
      });

      // 立即删除临时文档
      await this.db.collection(collectionName).doc(addResult._id).remove();
      
      return true;
    } catch (error) {
      console.error(`[数据库初始化器] 创建集合 ${collectionName} 失败:`, error);
      return false;
    }
  }

  /**
   * 获取集合的初始数据
   */
  getInitialData(collectionName) {
    const baseData = {
      _temp: true,
      _created_by: 'client_initializer',
      _created_at: new Date().toISOString(),
      _purpose: '客户端集合初始化'
    };

    switch (collectionName) {
      case 'behavior_records':
        return {
          ...baseData,
          teacherId: 'temp',
          studentId: 'temp',
          studentName: '临时记录',
          behavior: '初始化',
          score: 0,
          createTime: new Date()
        };
      
      case 'user_profiles':
        return {
          ...baseData,
          openid: 'temp',
          userInfo: {
            name: '临时用户',
            nickName: '临时'
          },
          updateTime: new Date()
        };
      
      case 'user_consent_records':
        return {
          ...baseData,
          openid: 'temp',
          consentType: 'init',
          consentStatus: true,
          consentTime: new Date()
        };
      
      default:
        return baseData;
    }
  }

  /**
   * 完整的数据库初始化流程
   */
  async initializeDatabase() {
    try {
      console.log('[数据库初始化器] 开始完整初始化流程...');
      
      // 1. 检查所有集合
      const checkResult = await this.checkAllCollections();
      
      if (checkResult.missing.length === 0) {
        console.log('[数据库初始化器] 所有集合都已存在，无需创建');
        return {
          success: true,
          message: '所有集合都已存在',
          existing: checkResult.existing,
          created: [],
          failed: []
        };
      }

      // 2. 创建缺失的集合
      console.log(`[数据库初始化器] 发现 ${checkResult.missing.length} 个缺失集合:`, checkResult.missing);
      
      const createResult = await this.createMissingCollections(checkResult.missing);
      
      // 3. 返回结果
      const result = {
        success: createResult.failed.length === 0,
        message: `创建了 ${createResult.created.length} 个集合，${createResult.failed.length} 个失败`,
        existing: checkResult.existing,
        created: createResult.created,
        failed: createResult.failed,
        errors: checkResult.errors
      };

      console.log('[数据库初始化器] 初始化完成:', result);
      return result;
      
    } catch (error) {
      console.error('[数据库初始化器] 初始化流程失败:', error);
      return {
        success: false,
        message: `初始化失败: ${error.message}`,
        error: error.message
      };
    }
  }
}

// 创建全局实例
const databaseInitializer = new DatabaseInitializer();

module.exports = {
  databaseInitializer,
  DatabaseInitializer
};

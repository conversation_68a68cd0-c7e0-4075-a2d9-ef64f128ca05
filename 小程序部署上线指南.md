# 📱 评语灵感君小程序部署上线指南

## 🎯 部署前准备检查清单

### 1. 账号准备
- [ ] 微信小程序账号（已完成主体认证）
- [ ] 腾讯云账号（用于云开发）
- [ ] 获取小程序AppID和AppSecret

### 2. 开发工具
- [ ] 微信开发者工具（最新版本）
- [ ] Node.js 环境（建议v16+）

## 🔧 核心配置文件设置

### 1. 更新 project.config.json
```json
{
  "appid": "你的实际小程序AppID",  // ⚠️ 必须替换！
  "projectname": "评语灵感君"
}
```

### 2. 检查 app.json 配置
- ✅ 页面路由配置完整
- ✅ 云开发功能已启用 (`"cloud": true`)
- ✅ tabBar配置正确

## 🌩️ 云开发环境配置

### 第一步：创建云开发环境
1. 登录[微信云开发控制台](https://cloud.weixin.qq.com/)
2. 创建新环境，环境名建议：`pingyu-prod`
3. 记录环境ID

### 第二步：配置环境变量
在各个云函数的 `config.json` 中配置：
```json
{
  "env": "你的环境ID",
  "runtime": "Nodejs16.13",
  "timeout": 60,
  "memorySize": 256
}
```

### 第三步：数据库初始化
需要创建以下集合：
- `users` - 用户信息
- `students` - 学生信息  
- `records` - 行为记录
- `comments` - 评语数据
- `templates` - 模板数据
- `statistics` - 统计数据
- `achievements` - 成就数据
- `system_config` - 系统配置

## 🚀 云函数部署流程

### 方法一：批量部署（推荐）
```bash
# 在项目根目录执行
cd cloudfunctions
# 部署所有云函数
for dir in */; do
  echo "部署 $dir"
  cd "$dir"
  npm install
  cd ..
done
```

### 方法二：逐个部署
在微信开发者工具中：
1. 右键每个云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 核心云函数列表：
- ✅ `login` - 用户登录
- ✅ `adminAPI` - 管理后台API
- ✅ `callDoubaoAPI` - AI接口调用
- ✅ `dataQuery` - 数据查询
- ✅ `generateComment` - 评语生成
- ✅ `getPromptTemplate` - 模板获取
- ✅ `initTemplateData` - 初始化数据

## 🔑 第三方API配置

### 豆包AI配置
在 `callDoubaoAPI` 云函数中配置：
```javascript
const API_CONFIG = {
  endpoint: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
  apiKey: '你的豆包API密钥',  // ⚠️ 必须配置！
  model: 'ep-20241201123045-5vblj'
}
```

## 📋 部署步骤详解

### 第1步：环境准备
```bash
# 检查Node.js版本
node -v  # 应该显示 v16+ 

# 检查微信开发者工具是否已安装并登录
```

### 第2步：项目配置
1. 用微信开发者工具打开项目
2. 修改 `project.config.json` 中的 `appid`
3. 检查云开发环境ID是否正确

### 第3步：云函数部署
1. 在开发者工具中打开"云开发"面板
2. 选择正确的环境
3. 逐个部署云函数，或使用批量部署脚本

### 第4步：数据库初始化
1. 在云开发控制台创建所需集合
2. 运行 `initTemplateData` 云函数初始化基础数据
3. 运行 `initDatabase` 云函数初始化系统配置

### 第5步：功能测试
- [ ] 用户登录功能
- [ ] 学生信息管理
- [ ] 评语生成功能
- [ ] 数据统计功能
- [ ] AI接口调用

### 第6步：性能优化
- [ ] 启用云函数并发
- [ ] 配置CDN加速
- [ ] 优化数据库索引
- [ ] 设置缓存策略

## 🚨 常见问题解决

### 问题1：云函数调用失败
**症状**：网络请求失败、超时
**解决**：
```javascript
// 检查云函数权限配置
wx.cloud.callFunction({
  name: 'functionName',
  data: {},
  success: res => console.log(res),
  fail: err => {
    console.error('云函数调用失败:', err);
    // 检查环境ID是否正确
    // 检查云函数是否部署成功
  }
})
```

### 问题2：数据库连接失败
**症状**：数据查询返回空或报错
**解决**：
1. 检查数据库权限设置
2. 确认集合名称正确
3. 检查索引配置

### 问题3：第三方API调用失败
**症状**：AI评语生成失败
**解决**：
1. 检查API密钥是否正确
2. 确认API额度是否充足
3. 检查网络环境和防火墙设置

## 📊 上线前最终检查

### 代码检查
- [ ] 删除所有调试代码和console.log
- [ ] 检查敏感信息是否泄露
- [ ] 确认所有页面功能正常

### 性能检查  
- [ ] 首页加载时间 < 3秒
- [ ] 云函数响应时间 < 2秒
- [ ] 内存使用合理

### 合规检查
- [ ] 用户隐私政策完整
- [ ] 数据收集说明清晰
- [ ] 符合小程序规范

## 🎯 提交审核

### 第1步：代码上传
1. 在开发者工具中点击"上传"
2. 填写版本号和备注
3. 确认上传成功

### 第2步：提交审核
1. 登录[微信小程序后台](https://mp.weixin.qq.com/)
2. 进入"版本管理"
3. 将开发版提交审核
4. 填写审核说明

### 第3步：发布上线
1. 审核通过后点击"发布"
2. 确认发布版本
3. 监控线上运行状态

## 🔄 后续维护

### 监控指标
- 用户活跃度
- 云函数调用量
- 错误日志
- 性能指标

### 更新流程
1. 开发新功能
2. 本地测试
3. 云函数部署
4. 代码上传
5. 提交审核
6. 发布上线

## ⚠️ 重要提醒

1. **AppID配置**：务必在 `project.config.json` 中配置正确的AppID
2. **环境隔离**：生产环境和开发环境要分开
3. **API密钥**：敏感信息不要硬编码，使用环境变量
4. **数据备份**：定期备份重要数据
5. **版本管理**：做好版本记录和回滚准备

## 🎉 部署完成！

恭喜！如果按照这个指南操作，你的"评语灵感君"小程序就可以成功上线了。记住，小程序上线只是开始，后续的用户反馈和功能迭代才是关键！

---

> 💡 **小白提示**：第一次部署可能会遇到各种问题，这很正常！遇到问题先查看错误日志，90%的问题都能通过日志找到原因。实在解决不了，可以到微信开发者社区寻求帮助。
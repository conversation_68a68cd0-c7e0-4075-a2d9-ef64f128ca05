/* 数据库修复页面样式 */
.repair-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 32rpx;
  color: #333;
}

/* 页面标题 */
.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
}

.back-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-right: 20rpx;
}

.back-icon {
  font-size: 36rpx;
  color: white;
  font-weight: bold;
}

.page-title {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  flex: 1;
  text-align: center;
  margin-right: 100rpx; /* 平衡返回按钮的宽度 */
}

/* 网络状态 */
.network-status {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
}

.status-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.status-text {
  font-size: 28rpx;
  color: #333;
}

/* 操作按钮区域 */
.action-section {
  margin-bottom: 40rpx;
}

.action-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 48rpx;
  margin-right: 30rpx;
  width: 80rpx;
  text-align: center;
}

.card-content {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.card-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 状态显示区域 */
.status-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-header {
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 结果列表 */
.results-list {
  margin-top: 20rpx;
}

.results-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.result-item {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}

.result-icon {
  font-size: 28rpx;
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.result-content {
  flex: 1;
}

.result-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.result-message {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.result-text {
  font-size: 26rpx;
  color: #333;
  flex: 1;
}

/* 重置按钮 */
.reset-section {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.reset-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
}

.reset-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.reset-text {
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

/* 帮助信息 */
.help-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
}

.help-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.help-text {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
  line-height: 1.5;
}

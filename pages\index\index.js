/**
 * AI评语助手 - 简化版首页
 * 专注核心功能，确保稳定运行
 */
const app = getApp();
const { networkManager } = require('../../utils/networkManager');

// 工具函数
function isMobile() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform !== 'devtools';
  } catch (error) {
    return true; // 默认认为是真机
  }
}

function isDevTools() {
  try {
    const systemInfo = wx.getSystemInfoSync();
    return systemInfo.platform === 'devtools';
  } catch (error) {
    return false;
  }
}

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {},
    displayName: '老师', // 格式化后的显示名称
    isGuestMode: false, // 是否为游客模式

    // 问候时间
    greetingTime: '',

    // 网络状态
    networkStatus: {
      isOnline: true,
      networkType: 'unknown',
      description: '检测中...',
      showOfflineNotice: false
    },

    // 效率统计
    efficiencyStats: {
      timeSaved: 0,       // 节省时间（分钟）
      commentsGenerated: 0, // 生成评语数
      qualityScore: 0     // 平均质量分
    },

    // 学生统计
    studentStats: {
      totalStudents: 0    // 学生总数
    },

    // 最近评语
    recentComments: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化网络监控
    this.initNetworkMonitoring();

    // 执行原有初始化逻辑
    this.initPage();

    // 【增长功能】延迟初始化增长功能，确保不影响现有逻辑
    setTimeout(() => {
      try {
        // 只在非真机环境或用户信息已加载后启用增长功能
        const isDevTool = isDevTools();

        if (isDevTool || this.data.userInfo.nickName) {
          const { growthEngine } = require('../../utils/growthEngine');
          growthEngine.init('pages/index/index');

          // 处理分享进入参数
          if (options.from && options.from.startsWith('share_')) {
            this.trackUserAction('share_click', { from: options.from });
          }

        } else {

          // 真机环境下进一步延迟
          setTimeout(() => {
            try {
              const { growthEngine } = require('../../utils/growthEngine');
              growthEngine.init('pages/index/index');

            } catch (e) {
              console.error('[首页] 增长功能延迟启用失败:', e);
            }
          }, 3000);
        }
      } catch (error) {
        console.error('[首页] 增长功能初始化失败，不影响正常使用:', error);
      }
    }, 2000); // 延迟2秒初始化，确保原有功能完全优先
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

    // 分阶段同步，确保真机环境兼容性
    this.forceSyncUserInfo();

    // 刷新学生统计数据
    this.loadStudentStats();

    // 延迟再次检查（真机环境需要）
    setTimeout(() => {

      this.getUserInfo();

      // 如果还是没有信息，最后一次尝试
      setTimeout(() => {
        const currentUserInfo = this.data.userInfo;
        if (!currentUserInfo || (!currentUserInfo.name && !currentUserInfo.nickName)) {

          this.forceSyncUserInfo();
        }
      }, 1000);
    }, 500);
    
    // 刷新其他数据
    this.refreshData();
    this.startDataRefreshTimer();
  },

  /**
   * 强制同步用户信息 - 真机与开发工具兼容
   */
  forceSyncUserInfo() {
    try {

      // 多层级获取用户信息，确保数据完整性
      const methods = [
        // 方法1：直接从存储获取
        () => wx.getStorageSync('userInfo'),
        // 方法2：从全局数据获取
        () => getApp().globalData.userInfo,
        // 方法3：从页面数据获取
        () => this.data.userInfo
      ];
      
      let userInfo = null;
      let sourceMethod = '';
      
      // 依次尝试各种获取方法
      for (let i = 0; i < methods.length; i++) {
        try {
          const result = methods[i]();
          if (result && (result.name || result.nickName || result.avatarUrl)) {
            userInfo = result;
            sourceMethod = `方法${i + 1}`;

            break;
          }
        } catch (error) {

        }
      }
      
      if (userInfo) {
        // 确保数据同步到所有位置
        this.setData({ userInfo });
        getApp().globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
        
        // 格式化显示名称
        const displayName = this.formatDisplayName(userInfo);
        this.setData({ displayName });

      } else {

        // 设置默认用户信息
        const defaultUserInfo = {
          name: '未设置姓名',
          nickName: '老师',
          avatarUrl: ''
        };
        
        this.setData({ 
          userInfo: defaultUserInfo,
          displayName: '老师'
        });

      }
    } catch (error) {
      console.error('[首页] 强制同步用户信息失败:', error);
    }
  },

  /**
   * 用户信息更新回调（由设置页面调用）
   */
  onUserInfoUpdate(userInfo) {

    // 立即更新用户信息到所有位置
    this.setData({ userInfo: userInfo });
    getApp().globalData.userInfo = userInfo;
    
    try {
      wx.setStorageSync('userInfo', userInfo);

    } catch (error) {
      console.error('[首页] 同步用户信息到存储失败:', error);
    }

    // 格式化并更新显示名称
    const displayName = this.formatDisplayName(userInfo);
    this.setData({ displayName });

  },

  /**
   * 初始化页面
   */
  async initPage() {
    try {
      // 首先检查登录状态
      const shouldShowLogin = await this.checkLoginStatus();
      if (shouldShowLogin) {
        return; // 如果需要登录，直接返回，不继续初始化
      }

      // 设置问候时间
      this.setGreetingTime();

      // 获取用户信息
      await this.getUserInfo();

      // 加载数据
      await this.refreshEfficiencyStats();
      await this.loadStudentStats();
      await this.loadRecentComments();

    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  /**
   * 检查登录状态
   * @returns {boolean} 是否需要显示登录页面
   */
  async checkLoginStatus() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      const isGuestMode = wx.getStorageSync('isGuestMode');

      // 如果既没有登录信息，也不是游客模式，则跳转到登录页
      if (!userInfo && !token && !isGuestMode) {

        wx.reLaunch({
          url: '/pages/login/login?from=/pages/index/index'
        });

        return true; // 需要显示登录页面
      }

      // 如果是游客模式，显示游客提示
      if (isGuestMode && !userInfo && !token) {

        this.setData({
          displayName: '游客',
          isGuestMode: true
        });
      }

      return false; // 不需要显示登录页面

    } catch (error) {
      console.error('[首页] 登录状态检查失败:', error);
      return false;
    }
  },

  /**
   * 设置问候时间
   */
  setGreetingTime() {
    const hour = new Date().getHours();
    let greeting = '';
    
    if (hour < 6) {
      greeting = '夜深了';
    } else if (hour < 9) {
      greeting = '早上好';
    } else if (hour < 12) {
      greeting = '上午好';
    } else if (hour < 14) {
      greeting = '中午好';
    } else if (hour < 17) {
      greeting = '下午好';
    } else if (hour < 19) {
      greeting = '傍晚好';
    } else {
      greeting = '晚上好';
    }
    
    this.setData({
      greetingTime: greeting
    });
  },

  /**
   * 获取用户信息 - 增强版，兼容真机环境
   */
  async getUserInfo() {
    try {

      // 多源数据获取
      const sources = {
        local: wx.getStorageSync('userInfo'),
        global: app.globalData.userInfo,
        page: this.data.userInfo
      };

      // 智能合并最完整的数据
      let userInfo = {};
      
      // 优先使用存储中的完整数据
      if (sources.local && (sources.local.name || sources.local.nickName)) {
        userInfo = { ...userInfo, ...sources.local };

      }
      
      // 补充全局数据
      if (sources.global && (sources.global.name || sources.global.nickName)) {
        userInfo = { ...userInfo, ...sources.global };

      }
      
      // 补充页面数据
      if (sources.page && (sources.page.name || sources.page.nickName)) {
        userInfo = { ...userInfo, ...sources.page };

      }
      
      // 真机环境特殊处理
      if (isMobile()) {

        // 真机环境下可能需要延迟获取
        if (!userInfo.name && !userInfo.nickName) {

          await new Promise(resolve => setTimeout(resolve, 100));
          
          // 再次尝试获取
          const retryLocal = wx.getStorageSync('userInfo');
          const retryGlobal = app.globalData.userInfo;
          
          if (retryLocal && (retryLocal.name || retryLocal.nickName)) {
            userInfo = { ...userInfo, ...retryLocal };

          } else if (retryGlobal && (retryGlobal.name || retryGlobal.nickName)) {
            userInfo = { ...userInfo, ...retryGlobal };

          }
        }
      }
      
      // 数据同步
      if (userInfo && (userInfo.name || userInfo.nickName)) {
        // 确保所有位置数据一致
        this.setData({ userInfo });
        app.globalData.userInfo = userInfo;
        
        try {
          wx.setStorageSync('userInfo', userInfo);
        } catch (syncError) {
          console.error('[首页] 同步用户信息到存储失败:', syncError);
        }
        
        // 格式化显示名称
        const displayName = this.formatDisplayName(userInfo);

      } else {

        // 使用默认信息
        const defaultInfo = {
          name: '未设置姓名',
          nickName: '老师',
          avatarUrl: ''
        };
        
        this.setData({
          userInfo: defaultInfo,
          displayName: '老师'
        });
      }
      
    } catch (error) {
      console.error('[首页] 获取用户信息异常:', error);
      
      // 异常时使用默认信息
      this.setData({
        userInfo: { nickName: '老师', name: '未设置姓名' },
        displayName: '老师'
      });
    }
  },

  /**
   * 格式化显示名称 - 简化版
   */
  formatDisplayName(userInfo) {

    // 数据验证
    if (!userInfo || typeof userInfo !== 'object') {

      return '老师';
    }

    // 获取名称
    let displayName = userInfo.name || userInfo.nickName;

    // 名称验证
    if (!displayName || 
        typeof displayName !== 'string' || 
        displayName.trim() === '' || 
        displayName === '未设置姓名' || 
        displayName === '用户') {

      return '老师';
    }

    // 清理名称
    displayName = displayName.trim();

    // 如果已经包含"老师"，直接返回
    if (displayName.includes('老师')) {

      return displayName;
    }

    // 提取姓氏（前1-2个字符）
    let surname = displayName;
    if (displayName.length > 2) {
      // 常见复姓处理
      const compoundSurnames = ['欧阳', '太史', '端木', '上官', '司马', '东方', '独孤', '南宫', '万俟', '闻人', '夏侯', '诸葛', '尉迟', '公羊', '赫连', '澹台', '皇甫', '宗政', '濮阳', '公冶', '太叔', '申屠', '公孙', '慕容', '仲孙', '钟离', '长孙', '宇文', '司徒', '鲜于'];
      
      let foundCompound = false;
      for (const compound of compoundSurnames) {
        if (displayName.startsWith(compound)) {
          surname = compound;
          foundCompound = true;
          break;
        }
      }
      
      if (!foundCompound) {
        surname = displayName.charAt(0);
      }
    }

    const formattedName = surname + '老师';

    return formattedName;
  },

  /**
   * 刷新效率统计
   */
  async refreshEfficiencyStats() {
    try {
      // 获取今日学生行为记录的平均评分
      const todayAverageScore = await this.getTodayStudentAverageScore();
      
      // 获取云服务实例
      const cloudService = this.getCloudService();
      if (!cloudService) {
        this.loadLocalStats(todayAverageScore);
        return;
      }

      // 从云数据库获取今日评语统计
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const result = await cloudService.getCommentList({
        startDate: startOfDay.toISOString(),
        endDate: endOfDay.toISOString(),
        pageSize: 1000 // 获取今日所有评语
      });

      if (result.success && result.data) {
        const todayComments = result.data.comments || [];
        const commentsGenerated = todayComments.length;
        const timeSaved = commentsGenerated * 3; // 假设每条评语节省3分钟

        this.setData({
          efficiencyStats: {
            timeSaved,
            commentsGenerated,
            qualityScore: todayAverageScore // 使用学生行为记录的平均评分
          }
        });

      } else {
        this.loadLocalStats(todayAverageScore);
      }

    } catch (error) {
      console.error('[首页] 刷新效率统计失败:', error);
      this.loadLocalStats(); // 降级到本地数据
    }
  },

  /**
   * 加载本地统计数据（降级方案）
   */
  loadLocalStats(todayAverageScore = null) {
    try {
      const savedComments = wx.getStorageSync('savedComments') || [];
      const recentComments = wx.getStorageSync('recentComments') || [];

      const commentsGenerated = savedComments.length + recentComments.length;
      const timeSaved = commentsGenerated * 3;
      
      // 如果没有传入今日平均评分，则从本地记录计算
      let qualityScore = todayAverageScore;
      if (qualityScore === null) {
        qualityScore = this.calculateLocalAverageScore();
      }

      this.setData({
        efficiencyStats: {
          timeSaved,
          commentsGenerated,
          qualityScore
        }
      });
    } catch (error) {
      console.error('[首页] 加载本地统计失败:', error);
    }
  },

  /**
   * 获取今日学生行为记录的平均评分 - 增强版
   */
  async getTodayStudentAverageScore() {
    try {
      // 获取当前用户ID
      const userId = await this.getCurrentUserId();

      // 计算今日的时间范围
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      // 从云数据库获取今日的学生行为记录
      const db = wx.cloud.database();

      // 先检查并确保集合存在
      let result;
      try {
        result = await db.collection('behavior_records')
          .where({
            teacherId: userId,
            createTime: db.command.gte(startOfDay).and(db.command.lt(endOfDay))
          })
          .field({
            score: true
          })
          .get();
      } catch (collectionError) {
        if (collectionError.errCode === -502005) {
          // 集合不存在，尝试初始化数据库
          console.log('[首页] behavior_records集合不存在，尝试初始化...');

          try {
            await this.initializeMissingCollections();
            console.log('[首页] 数据库初始化完成，重试查询...');

            // 重试查询
            result = await db.collection('behavior_records')
              .where({
                teacherId: userId,
                createTime: db.command.gte(startOfDay).and(db.command.lt(endOfDay))
              })
              .field({
                score: true
              })
              .get();
          } catch (initError) {
            console.error('[首页] 数据库初始化失败:', initError);
            console.log('[首页] behavior_records集合不存在，使用本地数据');
            return this.calculateLocalAverageScore();
          }
        } else {
          throw collectionError;
        }
      }

      if (result && result.data && result.data.length > 0) {
        // 计算平均评分
        const totalScore = result.data.reduce((sum, record) => sum + (record.score || 0), 0);
        const averageScore = totalScore / result.data.length;
        return Math.round(averageScore * 10) / 10; // 保留一位小数
      } else {
        // 如果没有今日记录，尝试从本地计算
        return this.calculateLocalAverageScore();
      }

    } catch (error) {
      console.error('[首页] 获取今日学生平均评分失败:', error);
      // 出错时返回本地计算的结果
      return this.calculateLocalAverageScore();
    }
  },

  /**
   * 初始化缺失的数据库集合
   */
  async initializeMissingCollections() {
    try {
      console.log('[首页] 开始初始化缺失的数据库集合...');

      const result = await this.callCloudFunctionWithRetry('initDatabase', {}, 2);

      if (result && result.code === 200) {
        console.log('[首页] 数据库初始化成功:', result.data);
        return true;
      } else {
        throw new Error(result?.message || '数据库初始化返回异常结果');
      }
    } catch (error) {
      console.error('[首页] 初始化数据库集合失败:', error);
      throw error;
    }
  },

  /**
   * 从本地数据计算平均评分
   */
  calculateLocalAverageScore() {
    try {
      // 从本地存储获取行为记录
      const localRecords = wx.getStorageSync('behavior_records') || [];
      
      if (localRecords.length === 0) {
        return 0; // 没有数据时显示0分而不是9.2
      }

      // 获取今日的记录
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

      const todayRecords = localRecords.filter(record => {
        const recordDate = new Date(record.createTime || record.timestamp);
        return recordDate >= startOfDay && recordDate < endOfDay;
      });

      if (todayRecords.length === 0) {
        return 0; // 没有今日数据时显示0分
      }

      // 计算平均评分
      const totalScore = todayRecords.reduce((sum, record) => sum + (record.score || 0), 0);
      const averageScore = totalScore / todayRecords.length;
      return Math.round(averageScore * 10) / 10; // 保留一位小数

    } catch (error) {
      console.error('[首页] 计算本地平均评分失败:', error);
      return 0; // 出错时显示0分
    }
  },

  /**
   * 获取云服务实例
   */
  getCloudService() {
    try {
      return app.globalData.cloudService;
    } catch (error) {
      console.error('[首页] 获取云服务失败:', error);
      return null;
    }
  },

  /**
   * 加载最近评语
   */
  async loadRecentComments() {
    try {
      // 获取云服务实例
      const cloudService = this.getCloudService();
      if (!cloudService) {

        this.loadLocalRecentComments();
        return;
      }

      // 从云数据库获取最近的评语
      const result = await cloudService.getCommentList({
        pageSize: 5, // 获取最近5条
        sortBy: 'createTime',
        sortOrder: 'desc'
      });

      if (result.success && result.data && result.data.comments) {
        const recentComments = result.data.comments.slice(0, 3).map(comment => ({
          id: comment._id,
          studentName: comment.studentName,
          content: comment.content.length > 50 ? comment.content.substring(0, 50) + '...' : comment.content,
          timeAgo: this.formatTimeAgo(comment.createTime)
        }));

        this.setData({
          recentComments
        });

      } else {

        this.loadLocalRecentComments();
      }

    } catch (error) {
      console.error('[首页] 加载最近评语失败:', error);
      this.loadLocalRecentComments(); // 降级到本地数据
    }
  },

  /**
   * 加载本地最近评语（降级方案）
   */
  loadLocalRecentComments() {
    try {
      const recentComments = wx.getStorageSync('recentComments') || [];
      this.setData({
        recentComments: recentComments.slice(0, 3) // 只显示最近3条
      });
    } catch (error) {
      console.error('[首页] 加载本地最近评语失败:', error);
    }
  },

  /**
   * 加载学生统计数据
   */
  async loadStudentStats() {
    try {

      // 获取当前用户ID
      const userId = await this.getCurrentUserId();

      // 从云数据库获取学生数量
      const db = wx.cloud.database();
      let result;
      try {
        result = await db.collection('students')
          .where({
            teacherId: userId  // 使用正确的字段名
          })
          .count();
      } catch (collectionError) {
        if (collectionError.errCode === -502005) {
          // 集合不存在，使用本地数据作为降级方案
          console.log('[首页] students集合不存在，使用本地数据');
          this.loadLocalStudentStats();
          return;
        }
        throw collectionError;
      }

      if (result.total !== undefined) {
        this.setData({
          'studentStats.totalStudents': result.total
        });

      } else {

        this.loadLocalStudentStats();
      }

    } catch (error) {
      console.error('[首页] 加载学生统计失败:', error);
      this.loadLocalStudentStats(); // 降级到本地数据
    }
  },

  /**
   * 获取当前用户ID - 增强版，支持重试和降级
   */
  async getCurrentUserId() {
    try {
      // 优先从全局数据获取
      const globalUserInfo = getApp().globalData.userInfo;
      if (globalUserInfo && globalUserInfo.openid) {
        console.log('[首页] 从全局数据获取用户ID成功');
        return globalUserInfo.openid;
      }

      // 从本地存储获取
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo && userInfo.openid) {
        console.log('[首页] 从本地存储获取用户ID成功');
        return userInfo.openid;
      }

      // 从缓存获取
      const cachedUserId = wx.getStorageSync('cached_user_id');
      if (cachedUserId && cachedUserId.length > 10) {
        console.log('[首页] 从缓存获取用户ID成功');
        return cachedUserId;
      }

      // 调用云函数获取（带重试机制）
      console.log('[首页] 开始调用云函数获取用户ID...');
      const res = await this.callCloudFunctionWithRetry('getUserId', {}, 3);

      if (res && res.openid) {
        console.log('[首页] 云函数获取用户ID成功');
        // 缓存用户ID
        wx.setStorageSync('cached_user_id', res.openid);

        // 更新全局数据
        const app = getApp();
        if (app.globalData.userInfo) {
          app.globalData.userInfo.openid = res.openid;
        } else {
          app.globalData.userInfo = { openid: res.openid };
        }

        return res.openid;
      }

      throw new Error('云函数返回无效的用户ID');
    } catch (error) {
      console.error('[首页] 获取用户ID失败:', error);

      // 最后的降级方案：生成临时用户ID
      const tempUserId = 'temp_user_' + Date.now();
      console.warn('[首页] 使用临时用户ID:', tempUserId);

      // 显示用户友好的提示
      wx.showToast({
        title: '网络连接异常，部分功能受限',
        icon: 'none',
        duration: 2000
      });

      return tempUserId;
    }
  },

  /**
   * 带重试机制的云函数调用
   */
  async callCloudFunctionWithRetry(name, data = {}, maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[首页] 第${attempt}次尝试调用云函数 ${name}`);

        const res = await new Promise((resolve, reject) => {
          wx.cloud.callFunction({
            name,
            data,
            timeout: 10000, // 10秒超时
            success: resolve,
            fail: reject
          });
        });

        if (res.result) {
          console.log(`[首页] 云函数 ${name} 调用成功`);
          return res.result;
        } else {
          throw new Error('云函数返回空结果');
        }
      } catch (error) {
        lastError = error;
        console.error(`[首页] 第${attempt}次调用云函数 ${name} 失败:`, error);

        if (attempt < maxRetries) {
          // 指数退避重试
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          console.log(`[首页] ${delay}ms后重试...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  },

  /**
   * 加载本地学生统计（降级方案）
   */
  loadLocalStudentStats() {
    try {
      const students = wx.getStorageSync('students') || [];

      this.setData({
        'studentStats.totalStudents': students.length
      });

      // 如果本地也没有数据，尝试从云端重新获取
      if (students.length === 0) {

        setTimeout(() => {
          this.loadStudentStats();
        }, 2000);
      }
    } catch (error) {
      console.error('[首页] 加载本地学生统计失败:', error);
    }
  },

  /**
   * 格式化时间为相对时间
   */
  formatTimeAgo(dateString) {
    try {
      const now = new Date();
      const date = new Date(dateString);
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMins < 1) return '刚刚';
      if (diffMins < 60) return `${diffMins}分钟前`;
      if (diffHours < 24) return `${diffHours}小时前`;
      if (diffDays < 7) return `${diffDays}天前`;
      return date.toLocaleDateString();
    } catch (error) {
      return '未知时间';
    }
  },

  /**
   * 刷新数据
   */
  async refreshData() {
    await Promise.all([
      this.getUserInfo(),
      this.refreshEfficiencyStats(),
      this.loadRecentComments()
    ]);
  },

  /**
   * 启动数据刷新定时器
   */
  startDataRefreshTimer() {
    // 清除现有定时器
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }

    // 每30秒刷新一次数据
    this.refreshTimer = setInterval(() => {
      this.refreshEfficiencyStats();
      this.loadRecentComments();
    }, 30000);
  },

  /**
   * 跳转到AI生成页面
   */
  goToGenerate(e) {

    // 记录用户行为（如果有增长功能）
    try {
      this.trackUserAction('click_ai_generate', { source: 'home_page' });
    } catch (trackError) {

    }
    
    try {
      // 先进行一些基础检查
      const pages = getCurrentPages();

      // 检查目标页面是否存在
      const targetPath = '/pages/comment/generate/generate';

      // 检查事件参数
      if (e && e.currentTarget && e.currentTarget.dataset) {

      }
      
      // 添加loading提示，增强用户体验
      wx.showLoading({
        title: '正在跳转...',
        mask: true
      });
      
      wx.navigateTo({
        url: targetPath,
        success: (res) => {

          wx.hideLoading();
        },
        fail: (err) => {
          console.error('[首页] 跳转失败:', err);
          wx.hideLoading();
          
          // 显示具体的错误信息
          let errorMsg = '跳转失败';
          if (err.errMsg) {
            if (err.errMsg.includes('page not found')) {
              errorMsg = 'AI生成页面不存在，请检查页面配置';
            } else if (err.errMsg.includes('exceed max page stack limit')) {
              errorMsg = '页面层级过深，请返回上级页面后重试';
            } else {
              errorMsg = `跳转失败: ${err.errMsg}`;
            }
          }
          
          wx.showModal({
            title: '跳转异常',
            content: errorMsg,
            showCancel: true,
            cancelText: '取消',
            confirmText: '重试',
            success: (modalRes) => {
              if (modalRes.confirm) {
                // 用户选择重试，再次尝试跳转
                setTimeout(() => {
                  this.goToGenerate();
                }, 500);
              }
            }
          });
        }
      });
      
    } catch (error) {
      console.error('[首页] AI生成按钮点击异常:', error);
      wx.hideLoading();
      wx.showToast({
        title: '系统异常，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到快速记录页面
   */
  goToQuickRecord(e) {

    // 记录用户行为
    try {
      this.trackUserAction('click_quick_record', { source: 'home_page' });
    } catch (trackError) {

    }
    
    try {
      const targetPath = '/pages/record/create/create';

      // 添加loading提示
      wx.showLoading({
        title: '正在跳转...',
        mask: true
      });
      
      wx.switchTab({
        url: targetPath,
        success: (res) => {

          wx.hideLoading();
        },
        fail: (err) => {
          console.error('[首页] 快速记录跳转失败:', err);
          wx.hideLoading();
          
          wx.showModal({
            title: '跳转异常',
            content: `跳转快速记录页面失败: ${err.errMsg || '未知错误'}`,
            showCancel: true,
            cancelText: '取消',
            confirmText: '重试',
            success: (modalRes) => {
              if (modalRes.confirm) {
                setTimeout(() => {
                  this.goToQuickRecord();
                }, 500);
              }
            }
          });
        }
      });
      
    } catch (error) {
      console.error('[首页] 快速记录按钮点击异常:', error);
      wx.hideLoading();
      wx.showToast({
        title: '系统异常，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到学生管理页面
   */
  goToStudentManage() {
    // 跳转前刷新一次数据
    this.loadStudentStats();

    wx.navigateTo({
      url: '/pages/student/list/list'
    });
  },

  /**
   * 手动刷新学生统计数据
   */
  async refreshStudentStats() {

    // 显示加载提示
    wx.showLoading({
      title: '刷新中...',
      mask: true
    });

    try {
      await this.loadStudentStats();
      wx.hideLoading();

      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.hideLoading();
      console.error('[首页] 手动刷新失败:', error);

      wx.showToast({
        title: '刷新失败，请重试',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 跳转到设置页面
   */
  goToSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  /**
   * 跳转到我的作品页面
   */
  goToWorks(e) {

    // 记录用户行为
    try {
      this.trackUserAction('click_my_works', { source: 'home_page' });
    } catch (trackError) {

    }
    
    try {
      const targetPath = '/pages/works/list/list';

      // 检查是否是tabBar页面
      const isTabBar = this.isTabBarPage(targetPath);

      // 添加loading提示
      wx.showLoading({
        title: '正在跳转...',
        mask: true
      });
      
      const navigateMethod = isTabBar ? wx.switchTab : wx.navigateTo;
      
      navigateMethod({
        url: targetPath,
        success: (res) => {

          wx.hideLoading();
        },
        fail: (err) => {
          console.error('[首页] 我的作品跳转失败:', err);
          wx.hideLoading();
          
          wx.showModal({
            title: '跳转异常',
            content: `跳转我的作品页面失败: ${err.errMsg || '未知错误'}`,
            showCancel: true,
            cancelText: '取消',
            confirmText: '重试',
            success: (modalRes) => {
              if (modalRes.confirm) {
                setTimeout(() => {
                  this.goToWorks();
                }, 500);
              }
            }
          });
        }
      });
      
    } catch (error) {
      console.error('[首页] 我的作品按钮点击异常:', error);
      wx.hideLoading();
      wx.showToast({
        title: '系统异常，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 检查是否为tabBar页面
   */
  isTabBarPage(path) {
    const tabBarPages = [
      'pages/index/index',
      'pages/comment/generate/generate',
      'pages/works/list/list',
      'pages/settings/settings'
    ];
    
    // 移除开头的斜杠进行比较
    const normalizedPath = path.startsWith('/') ? path.substring(1) : path;
    return tabBarPages.includes(normalizedPath);
  },

  /**
   * 页面分享 - 集成增长优化
   */
  onShareAppMessage() {
    try {
      // 【增长功能】使用优化的分享内容
      const optimizedContent = this.getOptimizedShareContent({
        title: 'AI评语助手 - 3分钟生成专业评语',
        path: '/pages/index/index'
      });

      // 记录分享行为
      this.trackUserAction('share_from_home');

      return optimizedContent;
    } catch (error) {
      console.error('[首页] 分享优化失败，使用原有内容:', error);
      // 降级到原有分享内容
      return {
        title: 'AI评语助手',
        path: '/pages/index/index'
      };
    }
  },

  /**
   * 长按调试功能
   */
  onLongPress() {
    this.debugPressCount = (this.debugPressCount || 0) + 1;

    if (this.debugPressCount >= 3) {
      // 重置计数器
      this.debugPressCount = 0;

      // 显示开发者调试工具
      this.debugUserInfo();

      // 清除计数器（5秒后重置）
      setTimeout(() => {
        this.debugPressCount = 0;
      }, 5000);
    } else {
      // 给开发者提示

    }
  },

  /**
   * 用户信息调试工具
   */
  debugUserInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();

      // 获取各种数据源
      const localData = wx.getStorageSync('userInfo');
      const globalData = getApp().globalData.userInfo;
      const pageData = this.data.userInfo;
      const displayName = this.data.displayName;

      const debugInfo = {
        platform: systemInfo.platform || '未知',
        local: localData ? `有数据 (${localData.name || localData.nickName || '无名称'})` : '无数据',
        global: globalData ? `有数据 (${globalData.name || globalData.nickName || '无名称'})` : '无数据',
        page: pageData ? `有数据 (${pageData.name || pageData.nickName || '无名称'})` : '无数据',
        display: displayName || '未设置'
      };

      wx.showModal({
        title: '用户信息调试',
        content: `平台: ${debugInfo.platform}\n本地: ${debugInfo.local}\n全局: ${debugInfo.global}\n页面: ${debugInfo.page}\n显示: ${debugInfo.display}`,
        cancelText: '关闭',
        confirmText: '重新同步',
        success: (res) => {
          if (res.confirm) {
            // 用户点击重新同步
            this.forceSyncUserInfo();

            setTimeout(() => {
              wx.showToast({
                title: '同步完成，请检查显示',
                icon: 'success',
                duration: 2000
              });
            }, 500);
          }
        }
      });
    } catch (error) {
      console.error('[首页] 调试工具异常:', error);
    }
  },

  /**
   * 【增长功能】获取优化的分享内容
   */
  getOptimizedShareContent(defaultContent) {
    try {
      const { growthEngine } = require('../../utils/growthEngine');
      return growthEngine.shareEngine.getShareContent('pages/index/index', defaultContent);
    } catch (error) {
      console.error('[首页] 获取优化分享内容失败:', error);
      return defaultContent;
    }
  },

  /**
   * 【增长功能】跟踪用户行为
   */
  trackUserAction(action, data = {}) {
    try {
      const { growthEngine } = require('../../utils/growthEngine');
      growthEngine.track(action, data);
    } catch (error) {
      console.error('[首页] 跟踪用户行为失败:', error);
    }
  },

  /**
   * 【增长功能】测试增长功能 - 开发模式专用
   */
  testGrowthFeatures() {
    try {
      const { testGrowthFeatures, showGrowthStats, checkGrowthConfig, generateGrowthReport } = require('../../utils/growthTest');

      wx.showActionSheet({
        itemList: ['测试所有功能', '查看增长统计', '重置增长数据', '模拟用户操作', '检查配置', '生成报告'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              const testResult = testGrowthFeatures();
              if (testResult.success) {
                wx.showToast({ title: '测试通过，查看控制台', icon: 'success' });
              } else {
                wx.showToast({ title: '测试失败: ' + testResult.error, icon: 'none' });
              }
              break;
            case 1:
              showGrowthStats();
              break;
            case 2:
              wx.showModal({
                title: '确认重置',
                content: '将清除所有增长功能数据，确定继续吗？',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    const { resetGrowthData } = require('../../utils/growthTest');
                    resetGrowthData();
                  }
                }
              });
              break;
            case 3:
              const { simulateUserActions } = require('../../utils/growthTest');
              simulateUserActions();
              wx.showToast({ title: '模拟操作已启动', icon: 'success' });
              break;
            case 4:
              const configStatus = checkGrowthConfig();
              const configText = Object.entries(configStatus)
                .map(([key, status]) => `${key}: ${status}`)
                .join('\n');
              wx.showModal({
                title: '增长功能配置',
                content: configText,
                showCancel: false
              });
              break;
            case 5:
              const report = generateGrowthReport();
              if (report) {
                wx.showToast({ title: '报告已生成，查看控制台', icon: 'success' });
              } else {
                wx.showToast({ title: '报告生成失败', icon: 'none' });
              }
              break;
          }
        }
      });
    } catch (error) {
      console.error('[首页] 增长功能测试失败:', error);
      wx.showToast({ title: '测试功能不可用', icon: 'none' });
    }
  },

  /**
   * 跳转到登录页面
   */
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login?from=/pages/index/index&action=login'
    });
  },

  /**
   * 【开发测试】跳转到增长功能测试页面
   */
  goToGrowthTest() {

    wx.navigateTo({
      url: '/pages/growth-test/growth-test'
    });
  },

  /**
   * 【系统监控】显示系统状态
   */
  showSystemStatus() {
    try {
      const app = getApp();
      const statusReport = app.getSystemStatusReport();
      
      if (!statusReport) {
        wx.showToast({
          title: '无法获取系统状态',
          icon: 'none'
        });
        return;
      }

      const currentStatus = statusReport.currentStatus;
      if (!currentStatus) {
        wx.showToast({
          title: '系统状态数据不可用',
          icon: 'none'
        });
        return;
      }

      // 构建状态显示文本
      const statusText = this.buildSystemStatusText(currentStatus);
      
      wx.showModal({
        title: `系统状态 - ${this.getHealthStatusText(currentStatus.overallHealth)}`,
        content: statusText,
        showCancel: true,
        cancelText: '关闭',
        confirmText: '刷新检查',
        success: (res) => {
          if (res.confirm) {
            this.triggerSystemCheck();
          }
        }
      });

    } catch (error) {
      console.error('[首页] 显示系统状态失败:', error);
      wx.showToast({
        title: '系统状态检查失败',
        icon: 'none'
      });
    }
  },

  /**
   * 构建系统状态显示文本
   */
  buildSystemStatusText(status) {
    const checks = status.checks || {};
    const statusItems = [];

    // 云服务状态
    if (checks.cloudService) {
      statusItems.push(`云服务: ${this.getStatusIcon(checks.cloudService.status)} ${checks.cloudService.message}`);
    }

    // 数据库状态
    if (checks.database) {
      statusItems.push(`数据库: ${this.getStatusIcon(checks.database.status)} ${checks.database.message}`);
    }

    // 实时同步状态
    if (checks.realTimeSync) {
      statusItems.push(`数据同步: ${this.getStatusIcon(checks.realTimeSync.status)} ${checks.realTimeSync.message}`);
    }

    // 网络状态
    if (checks.network) {
      statusItems.push(`网络: ${this.getStatusIcon(checks.network.status)} ${checks.network.message}`);
    }

    // 增长功能状态
    if (checks.growthFeatures) {
      statusItems.push(`增长功能: ${this.getStatusIcon(checks.growthFeatures.status)} ${checks.growthFeatures.message}`);
    }

    // 检查耗时
    if (status.checkDuration) {
      statusItems.push(`检查耗时: ${status.checkDuration}ms`);
    }

    return statusItems.join('\n');
  },

  /**
   * 获取状态图标
   */
  getStatusIcon(status) {
    const iconMap = {
      'healthy': '✅',
      'warning': '⚠️',
      'error': '❌',
      'unavailable': '⭕'
    };
    return iconMap[status] || '❓';
  },

  /**
   * 获取健康状态文本
   */
  getHealthStatusText(health) {
    const textMap = {
      'healthy': '正常',
      'warning': '警告',
      'error': '错误',
      'degraded': '降级',
      'unavailable': '不可用'
    };
    return textMap[health] || '未知';
  },

  /**
   * 手动触发系统检查
   */
  async triggerSystemCheck() {
    try {
      wx.showLoading({
        title: '检查系统状态...',
        mask: true
      });

      const app = getApp();
      const result = await app.triggerSystemCheck();
      
      wx.hideLoading();

      if (result) {
        const statusText = this.buildSystemStatusText(result);
        wx.showModal({
          title: `系统检查完成 - ${this.getHealthStatusText(result.overallHealth)}`,
          content: statusText,
          showCancel: false,
          confirmText: '确定'
        });
      } else {
        wx.showToast({
          title: '系统检查失败',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('[首页] 手动系统检查失败:', error);
      wx.showToast({
        title: '系统检查异常',
        icon: 'none'
      });
    }
  },

  /**
   * 显示数据库连接统计
   */
  showDatabaseStats() {
    try {
      const app = getApp();
      const dbManager = app.globalData.databaseConnectionManager;
      
      if (!dbManager) {
        wx.showToast({
          title: '数据库管理器不可用',
          icon: 'none'
        });
        return;
      }

      const stats = dbManager.getConnectionStats();
      
      const statsText = [
        `总连接数: ${stats.totalConnections}`,
        `健康连接: ${stats.healthyConnections}`,
        `异常连接: ${stats.unhealthyConnections}`,
        '',
        '连接详情:'
      ];

      stats.connections.forEach((conn, index) => {
        statsText.push(`${index + 1}. ${conn.name} ${conn.isHealthy ? '✅' : '❌'}`);
        statsText.push(`   创建时间: ${conn.createdAt}`);
        statsText.push(`   最后使用: ${conn.lastUsed}`);
      });

      wx.showModal({
        title: '数据库连接统计',
        content: statsText.join('\n'),
        showCancel: true,
        cancelText: '关闭',
        confirmText: '重连所有',
        success: (res) => {
          if (res.confirm) {
            this.reconnectAllDatabases();
          }
        }
      });

    } catch (error) {
      console.error('[首页] 显示数据库统计失败:', error);
      wx.showToast({
        title: '获取数据库统计失败',
        icon: 'none'
      });
    }
  },

  /**
   * 重连所有数据库连接
   */
  async reconnectAllDatabases() {
    try {
      wx.showLoading({
        title: '重连数据库...',
        mask: true
      });

      const app = getApp();
      const dbManager = app.globalData.databaseConnectionManager;
      
      await dbManager.reconnectAll();
      
      wx.hideLoading();
      wx.showToast({
        title: '数据库重连完成',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[首页] 数据库重连失败:', error);
      wx.showToast({
        title: '数据库重连失败',
        icon: 'none'
      });
    }
  },

  /**
   * 数据库连接测试（替代控制台测试）
   */
  async testDatabaseConnection() {
    try {
      wx.showLoading({
        title: '测试数据库连接...',
        mask: true
      });

      console.log('🔄 开始数据库连接测试');
      
      // 测试基础连接
      const db = wx.cloud.database();
      console.log('✅ 数据库对象创建成功');

      // 测试必要集合
      const collections = ['users', 'students', 'records', 'user_consent_records'];
      const results = [];
      
      for (const collectionName of collections) {
        try {
          const result = await db.collection(collectionName).limit(1).get();
          results.push(`✅ ${collectionName}: 连接正常`);
          console.log(`✅ ${collectionName} 集合测试通过`);
        } catch (error) {
          results.push(`❌ ${collectionName}: ${error.message}`);
          console.log(`❌ ${collectionName} 集合测试失败:`, error);
        }
      }

      // 测试云函数
      try {
        await wx.cloud.callFunction({
          name: 'login',
          data: { test: true }
        });
        results.push('✅ 云函数: 调用正常');
        console.log('✅ 云函数测试通过');
      } catch (error) {
        results.push(`❌ 云函数: ${error.message}`);
        console.log('❌ 云函数测试失败:', error);
      }

      wx.hideLoading();
      
      // 显示测试结果
      wx.showModal({
        title: '数据库连接测试结果',
        content: results.join('\n'),
        showCancel: false,
        confirmText: '确定'
      });

      console.log('🎉 数据库测试完成');
      
    } catch (error) {
      wx.hideLoading();
      console.error('[首页] 数据库测试失败:', error);
      wx.showModal({
        title: '测试失败',
        content: `数据库测试失败: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  /**
   * 初始化网络监控
   */
  initNetworkMonitoring() {
    try {
      // 获取初始网络状态
      this.updateNetworkStatus();

      // 添加网络状态监听器
      this.networkListener = networkManager.addListener((status) => {
        console.log('[首页] 网络状态变化:', status);
        this.handleNetworkStatusChange(status);
      });

      console.log('[首页] 网络监控初始化完成');
    } catch (error) {
      console.error('[首页] 网络监控初始化失败:', error);
    }
  },

  /**
   * 更新网络状态显示
   */
  async updateNetworkStatus() {
    try {
      const status = await networkManager.checkNetworkStatus();

      this.setData({
        'networkStatus.isOnline': status.isOnline,
        'networkStatus.networkType': status.networkType,
        'networkStatus.description': networkManager.getNetworkDescription(),
        'networkStatus.showOfflineNotice': !status.isOnline
      });

      console.log('[首页] 网络状态更新:', status);
    } catch (error) {
      console.error('[首页] 更新网络状态失败:', error);
    }
  },

  /**
   * 处理网络状态变化
   */
  handleNetworkStatusChange(status) {
    this.setData({
      'networkStatus.isOnline': status.isOnline,
      'networkStatus.networkType': status.networkType,
      'networkStatus.description': networkManager.getNetworkDescription(),
      'networkStatus.showOfflineNotice': !status.isOnline
    });

    if (status.changed) {
      if (status.isOnline && !status.wasOnline) {
        // 从离线恢复到在线
        wx.showToast({
          title: '网络已恢复',
          icon: 'success',
          duration: 2000
        });

        // 重新加载数据
        this.refreshAllData();
      } else if (!status.isOnline && status.wasOnline) {
        // 从在线变为离线
        wx.showToast({
          title: '网络连接异常',
          icon: 'none',
          duration: 3000
        });
      }
    }
  },

  /**
   * 刷新所有数据
   */
  async refreshAllData() {
    try {
      console.log('[首页] 开始刷新所有数据...');

      // 并行刷新各种数据
      await Promise.allSettled([
        this.getUserInfo(),
        this.loadStudentStats(),
        this.refreshEfficiencyStats()
      ]);

      console.log('[首页] 数据刷新完成');
    } catch (error) {
      console.error('[首页] 刷新数据失败:', error);
    }
  },

  /**
   * 运行云服务连接测试
   */
  async runCloudConnectionTest() {
    try {
      wx.showLoading({
        title: '正在测试云服务连接...',
        mask: true
      });

      const { testCloudConnection } = require('../../test/cloud-connection-test');
      const results = await testCloudConnection.runFullTest();

      wx.hideLoading();

      // 计算通过率
      const totalTests = Object.keys(results).length;
      const passedTests = Object.values(results).filter(Boolean).length;
      const passRate = Math.round((passedTests / totalTests) * 100);

      let title, content, icon;
      if (passRate === 100) {
        title = '测试完成';
        content = `🎉 所有测试通过！云服务连接正常。\n通过率: ${passRate}%`;
        icon = 'success';
      } else if (passRate >= 80) {
        title = '测试完成';
        content = `⚠️ 大部分功能正常，但仍有问题。\n通过率: ${passRate}%`;
        icon = 'none';
      } else {
        title = '测试完成';
        content = `❌ 多项测试失败，需要检查网络和配置。\n通过率: ${passRate}%`;
        icon = 'none';
      }

      wx.showModal({
        title,
        content,
        showCancel: false,
        confirmText: '确定'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[首页] 云服务连接测试失败:', error);

      wx.showModal({
        title: '测试失败',
        content: `测试过程中发生错误: ${error.message}`,
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  /**
   * 页面卸载时清理网络监听器
   */
  onUnload() {
    if (this.networkListener) {
      this.networkListener();
      this.networkListener = null;
    }
  }
});

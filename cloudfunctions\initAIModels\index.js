/**
 * 初始化多个AI模型配置
 * 在ai_configs集合中添加常用的AI模型
 */

const cloud = require('wx-server-sdk')

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 预设的AI模型配置
const AI_MODELS = [
  {
    name: '豆包AI Pro',
    displayName: '豆包AI Pro',
    provider: 'bytedance',
    model: 'doubao-pro-32k',
    status: 'active',  // 只有这个模型默认启用
    config: {
      apiKey: '',
      baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.0005,  // 每千token输入价格
    outputPrice: 0.002,  // 每千token输出价格
    usage: 0,
    totalCost: 0,
    isDefault: true,
    description: '字节跳动豆包AI，性价比高，适合日常使用'
  },
  {
    name: 'GPT-4 Turbo',
    displayName: 'GPT-4 Turbo',
    provider: 'openai',
    model: 'gpt-4-turbo-preview',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.01,
    outputPrice: 0.03,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: 'OpenAI最新GPT-4模型，理解能力强，适合复杂任务'
  },
  {
    name: 'GPT-3.5 Turbo',
    displayName: 'GPT-3.5 Turbo',
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://api.openai.com/v1',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.0015,
    outputPrice: 0.002,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: 'OpenAI经典模型，速度快，成本低'
  },
  {
    name: '文心一言',
    displayName: '文心一言',
    provider: 'baidu',
    model: 'ernie-bot-turbo',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.008,
    outputPrice: 0.012,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: '百度文心一言，中文理解能力强'
  },
  {
    name: '通义千问',
    displayName: '通义千问',
    provider: 'alibaba',
    model: 'qwen-turbo',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://dashscope.aliyuncs.com/api/v1',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.002,
    outputPrice: 0.006,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: '阿里通义千问，多模态能力强'
  },
  {
    name: 'Claude-3 Sonnet',
    displayName: 'Claude-3 Sonnet',
    provider: 'anthropic',
    model: 'claude-3-sonnet-20240229',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://api.anthropic.com/v1',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.003,
    outputPrice: 0.015,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: 'Anthropic Claude模型，安全性高，推理能力强'
  },
  {
    name: 'Gemini Pro',
    displayName: 'Gemini Pro',
    provider: 'google',
    model: 'gemini-pro',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://generativelanguage.googleapis.com/v1',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.0005,
    outputPrice: 0.0015,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: 'Google Gemini模型，多模态能力优秀'
  },
  {
    name: '智谱GLM-4',
    displayName: '智谱GLM-4',
    provider: 'zhipu',
    model: 'glm-4',
    status: 'inactive',
    config: {
      apiKey: '',
      baseUrl: 'https://open.bigmodel.cn/api/paas/v4',
      temperature: 0.7,
      maxTokens: 2000,
      topP: 0.9
    },
    inputPrice: 0.001,
    outputPrice: 0.002,
    usage: 0,
    totalCost: 0,
    isDefault: false,
    description: '智谱AI GLM-4模型，中文能力强'
  }
]

exports.main = async (event, context) => {
  console.log('开始初始化AI模型配置...')
  
  try {
    const results = []
    
    // 检查现有配置
    const existingModels = await db.collection('ai_configs').get()
    console.log(`当前ai_configs集合中有 ${existingModels.data.length} 个配置`)
    
    // 如果已有配置，询问是否要重置
    if (existingModels.data.length > 0 && !event.force) {
      return {
        code: 400,
        message: '检测到已有AI模型配置，如需重置请传入 force: true 参数',
        data: {
          existingCount: existingModels.data.length,
          existingModels: existingModels.data.map(m => ({
            name: m.name || m.displayName,
            provider: m.provider,
            status: m.status
          }))
        }
      }
    }
    
    // 如果是强制重置，先清空现有配置
    if (event.force && existingModels.data.length > 0) {
      console.log('强制重置：删除现有配置...')
      for (const model of existingModels.data) {
        await db.collection('ai_configs').doc(model._id).remove()
      }
      results.push(`删除了 ${existingModels.data.length} 个现有配置`)
    }
    
    // 添加新的AI模型配置
    console.log('开始添加新的AI模型配置...')
    let addedCount = 0
    
    for (const modelConfig of AI_MODELS) {
      try {
        const result = await db.collection('ai_configs').add({
          data: {
            ...modelConfig,
            createTime: db.serverDate(),
            updateTime: db.serverDate(),
            createTimestamp: Date.now(),
            updateTimestamp: Date.now()
          }
        })
        
        console.log(`✅ 添加模型: ${modelConfig.name}`)
        addedCount++
        
      } catch (error) {
        console.error(`❌ 添加模型失败: ${modelConfig.name}`, error)
        results.push(`添加模型失败: ${modelConfig.name} - ${error.message}`)
      }
    }
    
    results.push(`成功添加 ${addedCount} 个AI模型配置`)
    
    // 验证结果
    const finalCount = await db.collection('ai_configs').count()
    console.log(`初始化完成，ai_configs集合中现有 ${finalCount.total} 个配置`)
    
    return {
      code: 200,
      message: 'AI模型配置初始化成功',
      data: {
        addedCount,
        totalCount: finalCount.total,
        results,
        models: AI_MODELS.map(m => ({
          name: m.name,
          provider: m.provider,
          status: m.status,
          isDefault: m.isDefault
        }))
      }
    }
    
  } catch (error) {
    console.error('初始化AI模型配置失败:', error)
    return {
      code: 500,
      message: '初始化失败',
      error: error.message
    }
  }
}

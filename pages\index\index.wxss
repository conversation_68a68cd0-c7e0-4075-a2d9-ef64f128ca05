/* 原型图风格设计 */
.prototype-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #F0F2F5 0%, #E4E7ED 100%);
  padding: 40rpx 32rpx 120rpx;
  color: #333;
}

/* 网络状态提示 */
.network-notice {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #ff6b6b;
  color: white;
  padding: 20rpx 32rpx;
  animation: slideDown 0.3s ease-out;
}

.notice-content {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.notice-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
}

.notice-text {
  flex: 1;
  text-align: center;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 【开发测试】增长功能测试区域样式 */
.test-section {
  margin: 40rpx 0;
  padding: 0 16rpx;
}

.test-card {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e53 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.3);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.test-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.test-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 8rpx;
}

.test-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 用户头像区域 */
.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 48rpx;
  padding-top: 20rpx;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 32rpx;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 16rpx 48rpx rgba(84, 112, 198, 0.3);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 16rpx 48rpx rgba(84, 112, 198, 0.3);
}

.avatar-text {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.avatar-logo {
  width: 64rpx;
  height: 64rpx;
}

.greeting-content {
  flex: 1;
}

.greeting-main {
  font-size: 48rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.greeting-sub {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
  font-weight: 400;
}

/* 游客模式样式 */
.guest-tip {
  color: #5470C6 !important;
  font-weight: 600 !important;
}

.guest-login-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 8rpx 16rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.2);
  transition: all 0.3s ease;
}

.guest-login-tip:active {
  background: rgba(84, 112, 198, 0.2);
  transform: scale(0.98);
}

.login-tip-text {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 600;
}

/* ===== 原型图风格样式 ===== */

/* 今日统计 */
.stats-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 56rpx;
  font-weight: 700;
  color: #5470C6;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 核心功能 */
.function-section {
  margin-bottom: 32rpx;
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.function-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx;
  text-align: center;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transform: scale(1);
}

.function-card:active {
  transform: scale(0.95) translateY(1rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-card.primary {
  grid-column: span 2;
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.function-card.primary:active {
  transform: scale(0.97) translateY(1rpx);
  box-shadow: 0 6rpx 24rpx rgba(84, 112, 198, 0.4);
  background: linear-gradient(135deg, #4C5FBB 0%, #6AB3D8 100%);
}

.function-card.primary::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.function-card.primary:active::before {
  transform: scale(1);
  transition: transform 0.3s ease;
}

/* 主按钮的特殊动效增强 */
.function-card.primary .card-icon {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-card.primary:active .card-icon {
  transform: scale(0.9) rotate(5deg);
}

.function-card.primary .card-title {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-card.primary:active .card-title {
  transform: scale(0.98);
}

.card-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
}

.primary .card-icon {
  background: rgba(255, 255, 255, 0.2);
}

/* 快速记录按钮特殊样式 */
.function-card.quick-record .card-icon {
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
  box-shadow: 0 6rpx 20rpx rgba(145, 204, 117, 0.3);
}

.function-card.quick-record:active {
  transform: scale(0.96) translateY(1rpx);
  box-shadow: 0 8rpx 28rpx rgba(145, 204, 117, 0.4);
}

.function-card.quick-record::before {
  background: radial-gradient(circle, rgba(145, 204, 117, 0.2) 0%, transparent 70%);
}

/* 我的作品按钮特殊样式 */
.function-card.my-works .card-icon {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
  box-shadow: 0 6rpx 20rpx rgba(250, 200, 88, 0.3);
}

.function-card.my-works:active {
  transform: scale(0.96) translateY(1rpx);
  box-shadow: 0 8rpx 28rpx rgba(250, 200, 88, 0.4);
}

.function-card.my-works::before {
  background: radial-gradient(circle, rgba(250, 200, 88, 0.2) 0%, transparent 70%);
}

/* 默认普通卡片样式 */
.function-card:not(.primary):not(.quick-record):not(.my-works) .card-icon {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

/* 普通功能卡片的特殊动效 */
.function-card:not(.primary) {
  position: relative;
  overflow: hidden;
}

.function-card:not(.primary):active {
  transform: scale(0.96) translateY(1rpx);
  box-shadow: 0 6rpx 24rpx rgba(84, 112, 198, 0.25);
  background: rgba(255, 255, 255, 1);
}

/* 为普通卡片添加波纹效果 */
.function-card:not(.primary)::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(84, 112, 198, 0.15) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.4s ease;
  pointer-events: none;
  z-index: 1;
}

.function-card:not(.primary):active::before {
  width: 200%;
  height: 200%;
  transform: translate(-50%, -50%) scale(1);
  transition: transform 0.3s ease;
}

/* 图标在点击时的额外动效 */
.function-card .card-icon {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-card:active .card-icon {
  transform: scale(0.95);
}

/* 卡片内容的微妙移动 */
.function-card .card-title,
.function-card .card-desc {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.function-card:active .card-title {
  transform: translateY(-1rpx);
}

.function-card:active .card-desc {
  transform: translateY(1rpx);
}

/* 确保内容在波纹之上 */
.function-card .card-icon,
.function-card .card-title,
.function-card .card-desc {
  position: relative;
  z-index: 2;
}

/* 图标字体样式 */
.icon-font {
  font-size: 48rpx;
  line-height: 1;
}

.primary .card-icon .icon-font {
  color: white;
}

.function-card:not(.primary) .card-icon .icon-font {
  color: white;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
  color: #333;
}

.primary .card-title {
  color: white;
}

.card-desc {
  font-size: 24rpx;
  opacity: 0.8;
  display: block;
  color: #666;
  line-height: 1.3;
}

.primary .card-desc {
  color: rgba(255, 255, 255, 0.9);
}

/* 最近动态 */
.recent-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 40rpx 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.recent-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.recent-more {
  font-size: 28rpx;
  color: #5470C6;
  cursor: pointer;
  font-weight: 500;
}

.recent-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.recent-item:last-child {
  border-bottom: none;
}

.recent-student {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.recent-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recent-time {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

/* 新版单行口号样式 */
.greeting-sub-oneline {
  font-size: 30rpx;
  color: #2C3E50;
  font-weight: 500;
  letter-spacing: 1rpx;
  line-height: 1.5;
}

.highlight-word {
  font-size: 34rpx;
  color: #2C3E50;
  font-weight: 700;
}

/* 功能卡片区域 */
.function-cards-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.function-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 24rpx 40rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  min-height: 200rpx;
}

.function-card:active {
  transform: translateY(2rpx);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2rpx 12rpx rgba(84, 112, 198, 0.15);
}

.card-icon-wrapper {
  margin-bottom: 24rpx;
}

.card-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.icon-text {
  font-size: 40rpx;
  font-weight: 600;
  z-index: 2;
}

.ai-icon {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(84, 112, 198, 0.3);
}

.record-icon {
  background: linear-gradient(135deg, #91CC75 0%, #73C0DE 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(145, 204, 117, 0.3);
}

.works-icon {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
  color: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(250, 200, 88, 0.3);
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2C3E50;
  letter-spacing: 1rpx;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 22rpx;
  color: #606266;
  font-weight: 400;
  letter-spacing: 0.5rpx;
  opacity: 0.9;
}

/* 优雅的功能卡片设计 */
.elegant-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 28rpx;
  padding: 0;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8rpx 32rpx rgba(84, 112, 198, 0.12);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20rpx);
  overflow: hidden;
  position: relative;
  min-height: 260rpx;
}

.elegant-card:active {
  transform: translateY(-2rpx) scale(1.01);
  box-shadow: 0 12rpx 40rpx rgba(84, 112, 198, 0.18);
}

/* 卡片头部 */
.card-header {
  position: relative;
  padding: 24rpx 28rpx 16rpx 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-icon-elegant {
  width: 72rpx;
  height: 72rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(84, 112, 198, 0.25);
  transition: all 0.3s ease;
}

.card-badge {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 18rpx;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.ai-badge {
  background: linear-gradient(135deg, #5470C6 0%, #73C0DE 100%);
}

.record-badge {
  background: linear-gradient(135deg, #91CC75 0%, #5CB85C 100%);
}

.works-badge {
  background: linear-gradient(135deg, #FAC858 0%, #FC8452 100%);
}

/* 卡片内容 */
.card-content {
  padding: 0 32rpx 20rpx 32rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-title-elegant {
  font-size: 28rpx;
  font-weight: 700;
  color: #2C3E50;
  letter-spacing: 1rpx;
  margin-bottom: 6rpx;
  line-height: 1.3;
}

.card-subtitle {
  font-size: 20rpx;
  color: #5470C6;
  font-weight: 500;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
}

.card-description {
  font-size: 20rpx;
  color: #606266;
  line-height: 1.6;
  letter-spacing: 0.3rpx;
  opacity: 0.9;
  flex: 1;
}

/* 卡片底部 */
.card-footer {
  padding: 12rpx 28rpx 20rpx 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1rpx solid rgba(84, 112, 198, 0.08);
  background: rgba(84, 112, 198, 0.02);
}

.card-action {
  font-size: 22rpx;
  color: #5470C6;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}

.card-arrow {
  font-size: 20rpx;
  color: #5470C6;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.elegant-card:active .card-arrow {
  transform: translateX(4rpx);
}

/* 今日统计区域 */
.stats-section-clean {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 24rpx;
  padding: 24rpx 28rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.stats-title-clean {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  text-align: left;
  margin-bottom: 24rpx;
  letter-spacing: 1rpx;
}

.stats-row-clean {
  display: flex;
  align-items: center;
}

.stat-item-clean {
  flex: 1;
  text-align: center;
}

.stat-number-clean {
  display: block;
  font-size: 52rpx;
  font-weight: 600;
  color: #5470C6;
  margin-bottom: 8rpx;
  line-height: 1;
  text-shadow: 0 2rpx 8rpx rgba(84, 112, 198, 0.15);
}

.stat-label-clean {
  font-size: 24rpx;
  color: #606266;
  font-weight: 400;
  letter-spacing: 1rpx;
}

.stat-divider {
  width: 1rpx;
  height: 120rpx;
  background: rgba(84, 112, 198, 0.2);
  margin: 0 24rpx;
}

/* 最近动态区域 */
.recent-section-clean {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(84, 112, 198, 0.1);
  border: 1rpx solid rgba(84, 112, 198, 0.08);
  backdrop-filter: blur(10rpx);
}

.recent-header-clean {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.recent-title-clean {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
  letter-spacing: 1rpx;
}

.recent-more-clean {
  font-size: 24rpx;
  color: #5470C6;
  font-weight: 500;
  padding: 12rpx 20rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.recent-more-clean:active {
  background: rgba(84, 112, 198, 0.2);
  transform: scale(0.95);
}

.recent-list-clean {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recent-item-clean {
  padding: 32rpx;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 24rpx;
  border: 1rpx solid rgba(84, 112, 198, 0.05);
}

.recent-student-clean {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #2C3E50;
  margin-bottom: 12rpx;
  letter-spacing: 1rpx;
}

.recent-content-clean {
  display: block;
  font-size: 24rpx;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 12rpx;
  font-weight: 400;
}

.recent-time-clean {
  font-size: 22rpx;
  color: #909399;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .prototype-page {
    padding: 32rpx 24rpx 80rpx;
  }

  .morandi-page {
    padding: 32rpx 24rpx;
  }
  
  .header-section {
    margin-bottom: 40rpx;
    padding-top: 40rpx;
  }
  
  .user-avatar-large {
    width: 120rpx;
    height: 120rpx;
  }
  
  .greeting-main {
    font-size: 52rpx;
  }
  
  .greeting-sub {
    font-size: 28rpx;
  }
  
  .function-cards-row {
    gap: 12rpx;
    margin-bottom: 20rpx;
  }
  
  .function-card {
    padding: 40rpx 20rpx;
  }
  
  .card-icon {
    width: 80rpx;
    height: 80rpx;
  }
  
  .icon-text {
    font-size: 32rpx;
  }
  
  .card-title {
    font-size: 24rpx;
    margin-bottom: 6rpx;
  }

  .card-desc {
    font-size: 20rpx;
  }
  
  .stats-section-clean {
    padding: 20rpx 24rpx;
    margin-bottom: 20rpx;
  }
  
  .stats-title-clean {
    font-size: 32rpx;
    margin-bottom: 32rpx;
  }
  
  .stat-number-clean {
    font-size: 64rpx;
  }
  
  .stat-label-clean {
    font-size: 22rpx;
  }
  
  .stat-divider {
    height: 80rpx;
    margin: 0 16rpx;
  }
  
  .recent-section-clean {
    padding: 40rpx 24rpx;
  }
  
  .recent-item-clean {
    padding: 24rpx;
  }
}

/* 学生管理横向大卡片 */
.student-manage-card {
  margin-top: 16rpx;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  padding: 32rpx;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transform: scale(1);
}

.student-manage-card:active {
  transform: scale(0.98) translateY(1rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.student-card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.student-card-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.student-manage-card .student-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(84, 112, 198, 0.1);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: #5470C6;
}

.student-manage-card .student-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.student-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50;
}

.student-desc {
  font-size: 26rpx;
  color: #909399;
}

.student-card-right {
  display: flex;
  align-items: center;
  gap: 12rpx;
  color: #5470C6;
}

.student-count {
  font-size: 48rpx;
  font-weight: 700;
  line-height: 1;
}

.student-unit {
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.student-arrow {
  font-size: 32rpx;
  font-weight: 600;
  transition: transform 0.3s ease;
  color: #909399;
}

.student-manage-card:active .student-arrow {
  transform: translateX(8rpx);
}
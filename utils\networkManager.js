/**
 * 网络状态管理器
 * 监控网络状态，提供网络检测和重连机制
 */

class NetworkManager {
  constructor() {
    this.isOnline = true;
    this.networkType = 'unknown';
    this.listeners = [];
    this.retryQueue = [];
    this.isRetrying = false;
    
    this.init();
  }

  /**
   * 初始化网络监控
   */
  init() {
    // 获取初始网络状态
    this.checkNetworkStatus();
    
    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('[网络管理器] 网络状态变化:', res);
      
      const wasOnline = this.isOnline;
      this.isOnline = res.isConnected;
      this.networkType = res.networkType;
      
      // 通知监听器
      this.notifyListeners({
        isOnline: this.isOnline,
        networkType: this.networkType,
        wasOnline,
        changed: wasOnline !== this.isOnline
      });
      
      // 如果从离线恢复到在线，处理重试队列
      if (!wasOnline && this.isOnline) {
        this.processRetryQueue();
      }
    });
  }

  /**
   * 检查当前网络状态
   */
  async checkNetworkStatus() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        });
      });
      
      this.isOnline = res.networkType !== 'none';
      this.networkType = res.networkType;
      
      console.log('[网络管理器] 当前网络状态:', {
        isOnline: this.isOnline,
        networkType: this.networkType
      });
      
      return {
        isOnline: this.isOnline,
        networkType: this.networkType
      };
    } catch (error) {
      console.error('[网络管理器] 检查网络状态失败:', error);
      return {
        isOnline: false,
        networkType: 'unknown'
      };
    }
  }

  /**
   * 添加网络状态监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    // 返回移除监听器的函数
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 通知所有监听器
   */
  notifyListeners(status) {
    this.listeners.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('[网络管理器] 监听器回调失败:', error);
      }
    });
  }

  /**
   * 检测云服务连接状态
   */
  async testCloudConnection() {
    try {
      if (!this.isOnline) {
        throw new Error('设备离线');
      }

      // 尝试调用一个简单的云函数来测试连接
      const startTime = Date.now();
      
      const res = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getUserId',
          timeout: 5000,
          success: resolve,
          fail: reject
        });
      });

      const responseTime = Date.now() - startTime;
      
      return {
        success: true,
        responseTime,
        result: res.result
      };
    } catch (error) {
      console.error('[网络管理器] 云服务连接测试失败:', error);
      return {
        success: false,
        error: error.message,
        errorCode: error.errCode
      };
    }
  }

  /**
   * 添加到重试队列
   */
  addToRetryQueue(task) {
    this.retryQueue.push({
      ...task,
      addedAt: Date.now(),
      retryCount: 0
    });
    
    // 如果网络可用，立即处理队列
    if (this.isOnline && !this.isRetrying) {
      this.processRetryQueue();
    }
  }

  /**
   * 处理重试队列
   */
  async processRetryQueue() {
    if (this.isRetrying || this.retryQueue.length === 0) {
      return;
    }

    this.isRetrying = true;
    console.log(`[网络管理器] 开始处理重试队列，共${this.retryQueue.length}个任务`);

    while (this.retryQueue.length > 0 && this.isOnline) {
      const task = this.retryQueue.shift();
      
      try {
        await task.execute();
        console.log('[网络管理器] 重试任务执行成功:', task.name);
      } catch (error) {
        console.error('[网络管理器] 重试任务执行失败:', task.name, error);
        
        // 如果重试次数未达到上限，重新加入队列
        if (task.retryCount < (task.maxRetries || 3)) {
          task.retryCount++;
          this.retryQueue.push(task);
        }
      }
    }

    this.isRetrying = false;
    console.log('[网络管理器] 重试队列处理完成');
  }

  /**
   * 获取网络状态描述
   */
  getNetworkDescription() {
    if (!this.isOnline) {
      return '网络未连接';
    }

    switch (this.networkType) {
      case 'wifi':
        return 'WiFi网络';
      case '2g':
        return '2G网络';
      case '3g':
        return '3G网络';
      case '4g':
        return '4G网络';
      case '5g':
        return '5G网络';
      default:
        return '未知网络';
    }
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isOnline: this.isOnline,
      networkType: this.networkType,
      description: this.getNetworkDescription(),
      retryQueueLength: this.retryQueue.length,
      isRetrying: this.isRetrying
    };
  }
}

// 创建全局实例
const networkManager = new NetworkManager();

module.exports = {
  networkManager,
  NetworkManager
};

<!--
  AI评语助手 - 原型图风格设计
  按照高保真原型图重新设计的首页布局
-->
<view class="prototype-page">
  <!-- 网络状态提示 -->
  <view class="network-notice" wx:if="{{networkStatus.showOfflineNotice}}">
    <view class="notice-content">
      <text class="notice-icon">⚠️</text>
      <text class="notice-text">网络连接异常，部分功能可能受限</text>
    </view>
  </view>

  <!-- 用户问候区域 -->
  <view class="header-section">
    <view class="user-avatar" bindtap="onAvatarTap">
      <image
        wx:if="{{userInfo.avatarUrl}}"
        class="avatar-image"
        src="{{userInfo.avatarUrl}}"
        mode="aspectFill"
        lazy-load="true"
      />
      <view wx:else class="avatar-placeholder">
        <image
          wx:if="{{isGuestMode}}"
          src="/images/logo.svg"
          class="avatar-logo"
          mode="aspectFit"
        />
        <text wx:else class="avatar-text">{{displayName.charAt(0)}}</text>
      </view>
    </view>

    <view class="greeting-content">
      <view class="greeting-main">{{greetingTime}}，{{displayName}}</view>
      <view class="greeting-sub" wx:if="{{!isGuestMode}}">让每一句评语都充满温度</view>
      <view class="greeting-sub guest-tip" wx:if="{{isGuestMode}}">体验模式 · 点击登录解锁完整功能</view>

      <!-- 游客模式登录提示 -->
      <view wx:if="{{isGuestMode}}" class="guest-login-tip" bindtap="goToLogin">
        <simple-icon name="user-circle-o" size="16px" color="#5470C6" />
        <text class="login-tip-text">立即登录</text>
      </view>
    </view>
  </view>

  <!-- 今日统计 -->
  <view class="stats-section">
    <view class="stats-title">今日统计</view>
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-number">{{efficiencyStats.commentsGenerated}}</text>
        <text class="stat-label">条评语</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{efficiencyStats.timeSaved}}</text>
        <text class="stat-label">分钟节省</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{efficiencyStats.qualityScore || '0'}}</text>
        <text class="stat-label">今日均分</text>
      </view>
    </view>
  </view>

  <!-- 核心功能 -->
  <view class="function-section">
    <view class="function-grid">
      <view class="function-card primary" bindtap="goToGenerate" data-action="ai-generate">
        <view class="card-icon" bindtap="goToGenerate" data-action="ai-generate">
          <text class="icon-font">✨</text>
        </view>
        <text class="card-title" bindtap="goToGenerate" data-action="ai-generate">AI智能生成</text>
        <text class="card-desc" bindtap="goToGenerate" data-action="ai-generate">3分钟生成专业评语</text>
      </view>

      <view class="function-card quick-record" bindtap="goToQuickRecord" data-action="quick-record">
        <view class="card-icon" bindtap="goToQuickRecord" data-action="quick-record">
          <text class="icon-font">⚡</text>
        </view>
        <text class="card-title" bindtap="goToQuickRecord" data-action="quick-record">快速记录</text>
        <text class="card-desc" bindtap="goToQuickRecord" data-action="quick-record">记录学生表现</text>
      </view>

      <view class="function-card my-works" bindtap="goToWorks" data-action="my-works">
        <view class="card-icon" bindtap="goToWorks" data-action="my-works">
          <text class="icon-font">📄</text>
        </view>
        <text class="card-title" bindtap="goToWorks" data-action="my-works">我的作品</text>
        <text class="card-desc" bindtap="goToWorks" data-action="my-works">管理评语作品</text>
      </view>
    </view>

    <!-- 学生管理 - 横向大卡片 -->
    <view class="student-manage-card" bindtap="goToStudentManage">
      <view class="student-card-content">
        <view class="student-card-left">
          <view class="student-icon">
            <text class="icon-font">👥</text>
          </view>
          <view class="student-info">
            <view class="student-title">学生管理</view>
            <view class="student-desc">管理班级学生信息</view>
          </view>
        </view>
        <view class="student-card-right">
          <view class="student-count">{{studentStats.totalStudents || 0}}</view>
          <view class="student-unit">名学生</view>
          <view class="student-arrow">→</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近动态 -->
  <view class="recent-section" wx:if="{{recentComments.length > 0}}">
    <view class="recent-header">
      <view class="recent-title">最近动态</view>
      <view class="recent-more" bindtap="goToWorks">查看全部</view>
    </view>

    <view class="recent-item" wx:for="{{recentComments}}" wx:key="id" wx:for-index="idx" wx:if="{{idx < 2}}">
      <view class="recent-student">{{item.studentName}}</view>
      <view class="recent-content">{{item.content}}</view>
      <view class="recent-time">{{item.timeAgo}}</view>
    </view>
  </view>

  <!-- 【隐藏测试入口】增长功能测试 - 仅开发者可见 -->
  <view class="test-section" wx:if="{{false}}" style="display: none;">
    <view class="test-card" bindtap="goToGrowthTest">
      <view class="test-icon">🧪</view>
      <text class="test-title">增长功能测试</text>
      <text class="test-desc">测试500UV增长目标功能</text>
    </view>
  </view>

  <!-- 【开发测试】隐藏的增长功能测试入口 - 长按标题栏激活 -->
  <view class="hidden-test-trigger" bindlongpress="goToGrowthTest" style="position: absolute; top: 0; left: 0; width: 100%; height: 100rpx; z-index: -10; pointer-events: none;"></view>

  <!-- 开发者工具面板 -->
  <view class="dev-tools-panel" wx:if="{{showDevTools}}">
    <view class="dev-tools-header">
      <text class="dev-tools-title">🔧 开发者工具</text>
      <view class="dev-tools-close" bindtap="closeDevTools">✕</view>
    </view>

    <view class="dev-tools-content">
      <view class="dev-tool-item" bindtap="quickCheckDatabase">
        <text class="tool-icon">🔍</text>
        <text class="tool-name">快速检查数据库</text>
      </view>

      <view class="dev-tool-item" bindtap="repairDatabase">
        <text class="tool-icon">🔧</text>
        <text class="tool-name">修复数据库</text>
      </view>

      <view class="dev-tool-item" bindtap="testDatabaseConnection">
        <text class="tool-icon">🔗</text>
        <text class="tool-name">测试数据库连接</text>
      </view>

      <view class="dev-tool-item" bindtap="runCloudConnectionTest">
        <text class="tool-icon">☁️</text>
        <text class="tool-name">云服务连接测试</text>
      </view>

      <view class="dev-tool-item" bindtap="showDatabaseMenu">
        <text class="tool-icon">📋</text>
        <text class="tool-name">数据库管理菜单</text>
      </view>
    </view>
  </view>
</view>
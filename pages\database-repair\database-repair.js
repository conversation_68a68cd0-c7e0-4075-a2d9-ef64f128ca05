/**
 * 数据库修复页面
 * 专门用于修复数据库连接和集合问题
 */

Page({
  data: {
    repairStatus: 'ready', // ready, checking, repairing, completed, failed
    checkResults: [],
    repairResults: [],
    networkStatus: {
      isOnline: true,
      description: '检测中...'
    }
  },

  onLoad() {
    console.log('[数据库修复] 页面加载');
    this.checkNetworkStatus();
  },

  /**
   * 检查网络状态
   */
  async checkNetworkStatus() {
    try {
      const res = await new Promise((resolve, reject) => {
        wx.getNetworkType({
          success: resolve,
          fail: reject
        });
      });

      this.setData({
        'networkStatus.isOnline': res.networkType !== 'none',
        'networkStatus.description': res.networkType === 'none' ? '网络未连接' : `${res.networkType}网络`
      });
    } catch (error) {
      console.error('[数据库修复] 网络状态检查失败:', error);
    }
  },

  /**
   * 快速检查数据库状态
   */
  async quickCheck() {
    this.setData({
      repairStatus: 'checking',
      checkResults: []
    });

    wx.showLoading({
      title: '正在检查...',
      mask: true
    });

    try {
      const { databaseRepairTool } = require('../../utils/databaseRepairTool');
      
      // 检查关键集合
      const keyCollections = ['behavior_records', 'students', 'user_profiles', 'users'];
      const results = [];
      const db = wx.cloud.database();

      for (const collection of keyCollections) {
        try {
          await db.collection(collection).limit(1).get();
          results.push({
            name: collection,
            status: 'success',
            message: '集合存在且可访问'
          });
        } catch (error) {
          if (error.errCode === -502005) {
            results.push({
              name: collection,
              status: 'missing',
              message: '集合不存在'
            });
          } else {
            results.push({
              name: collection,
              status: 'error',
              message: error.message
            });
          }
        }
      }

      this.setData({
        repairStatus: 'completed',
        checkResults: results
      });

      wx.hideLoading();

      // 检查是否有问题需要修复
      const hasIssues = results.some(r => r.status !== 'success');
      if (hasIssues) {
        wx.showModal({
          title: '检查完成',
          content: '发现数据库问题，是否立即修复？',
          confirmText: '立即修复',
          cancelText: '稍后处理',
          success: (res) => {
            if (res.confirm) {
              this.repairDatabase();
            }
          }
        });
      } else {
        wx.showToast({
          title: '数据库状态正常',
          icon: 'success'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('[数据库修复] 快速检查失败:', error);
      
      this.setData({
        repairStatus: 'failed'
      });

      wx.showModal({
        title: '检查失败',
        content: `检查过程中发生错误: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 修复数据库
   */
  async repairDatabase() {
    this.setData({
      repairStatus: 'repairing',
      repairResults: []
    });

    wx.showLoading({
      title: '正在修复数据库...',
      mask: true
    });

    try {
      const { databaseRepairTool } = require('../../utils/databaseRepairTool');
      const results = await databaseRepairTool.repairDatabase();

      wx.hideLoading();

      this.setData({
        repairStatus: 'completed',
        repairResults: results || []
      });

      wx.showToast({
        title: '修复完成',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[数据库修复] 修复失败:', error);
      
      this.setData({
        repairStatus: 'failed'
      });

      wx.showModal({
        title: '修复失败',
        content: `修复过程中发生错误: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 测试数据库连接
   */
  async testConnection() {
    wx.showLoading({
      title: '测试连接中...',
      mask: true
    });

    try {
      const db = wx.cloud.database();
      
      // 尝试获取数据库信息
      await db.collection('users').limit(1).get();
      
      wx.hideLoading();
      wx.showToast({
        title: '数据库连接正常',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[数据库修复] 连接测试失败:', error);
      
      let message = '数据库连接失败';
      if (error.errCode === -502005) {
        message = 'users集合不存在，需要修复数据库';
      } else {
        message = `连接错误: ${error.message}`;
      }

      wx.showModal({
        title: '连接测试失败',
        content: message,
        confirmText: '修复数据库',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.repairDatabase();
          }
        }
      });
    }
  },

  /**
   * 运行完整测试
   */
  async runFullTest() {
    wx.showLoading({
      title: '运行完整测试...',
      mask: true
    });

    try {
      const { testCloudConnection } = require('../../test/cloud-connection-test');
      const results = await testCloudConnection.runFullTest();

      wx.hideLoading();

      // 计算通过率
      const totalTests = Object.keys(results).length;
      const passedTests = Object.values(results).filter(Boolean).length;
      const passRate = Math.round((passedTests / totalTests) * 100);

      let title, content;
      if (passRate === 100) {
        title = '测试完成';
        content = `🎉 所有测试通过！\n通过率: ${passRate}%`;
      } else if (passRate >= 80) {
        title = '测试完成';
        content = `⚠️ 大部分功能正常\n通过率: ${passRate}%`;
      } else {
        title = '测试完成';
        content = `❌ 多项测试失败\n通过率: ${passRate}%\n建议进行数据库修复`;
      }

      wx.showModal({
        title,
        content,
        confirmText: passRate < 100 ? '修复数据库' : '确定',
        cancelText: passRate < 100 ? '稍后处理' : '',
        showCancel: passRate < 100,
        success: (res) => {
          if (res.confirm && passRate < 100) {
            this.repairDatabase();
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('[数据库修复] 完整测试失败:', error);
      
      wx.showModal({
        title: '测试失败',
        content: `测试过程中发生错误: ${error.message}`,
        showCancel: false
      });
    }
  },

  /**
   * 重置状态
   */
  resetStatus() {
    this.setData({
      repairStatus: 'ready',
      checkResults: [],
      repairResults: []
    });
  },

  /**
   * 返回首页
   */
  goBack() {
    wx.navigateBack({
      fail: () => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }
    });
  }
});
